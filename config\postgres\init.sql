-- PostgreSQL Initialization Script for CCTV MLOps
-- Includes PostGIS setup and initial schema

-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
CREATE EXTENSION IF NOT EXISTS postgis_tiger_geocoder;

-- Create database user with appropriate permissions
-- ALTER USER mlops_user WITH SUPERUSER;

-- <PERSON><PERSON> schemas
CREATE SCHEMA IF NOT EXISTS cctv;
CREATE SCHEMA IF NOT EXISTS monitoring;
CREATE SCHEMA IF NOT EXISTS geospatial;

-- Set search path
ALTER DATABASE cctv_mlops SET search_path TO cctv, monitoring, geospatial, public;

-- Grant schema permissions
GRANT ALL ON SCHEMA cctv TO mlops_user;
GRANT ALL ON SCHEMA monitoring TO mlops_user;
GRANT ALL ON SCHEMA geospatial TO mlops_user;

-- Trucks table
CREATE TABLE IF NOT EXISTS cctv.trucks (
    id SERIAL PRIMARY KEY,
    truck_number VARCHAR(20) UNIQUE NOT NULL,
    driver_name VARCHAR(100) NOT NULL,
    license_plate VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    current_location GEOMETRY(POINT, 4326),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cameras table
CREATE TABLE IF NOT EXISTS cctv.cameras (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER REFERENCES cctv.trucks(id) ON DELETE CASCADE,
    camera_position VARCHAR(20) NOT NULL CHECK (camera_position IN ('front', 'rear', 'left', 'right')),
    stream_url VARCHAR(500) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    resolution VARCHAR(20) DEFAULT '1280x720',
    fps INTEGER DEFAULT 5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(truck_id, camera_position)
);

-- Detections table
CREATE TABLE IF NOT EXISTS cctv.detections (
    id SERIAL PRIMARY KEY,
    camera_id INTEGER REFERENCES cctv.cameras(id) ON DELETE CASCADE,
    truck_id INTEGER REFERENCES cctv.trucks(id) ON DELETE CASCADE,
    object_type VARCHAR(50) NOT NULL,
    confidence DECIMAL(4,3) NOT NULL,
    bounding_box JSONB,
    location GEOMETRY(POINT, 4326),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    frigate_event_id VARCHAR(100),
    snapshot_url VARCHAR(500),
    video_url VARCHAR(500)
);

-- Violations table
CREATE TABLE IF NOT EXISTS cctv.violations (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER REFERENCES cctv.trucks(id) ON DELETE CASCADE,
    camera_id INTEGER REFERENCES cctv.cameras(id) ON DELETE CASCADE,
    detection_id INTEGER REFERENCES cctv.detections(id) ON DELETE SET NULL,
    violation_type VARCHAR(50) NOT NULL,
    description TEXT,
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    confidence DECIMAL(4,3) NOT NULL,
    location GEOMETRY(POINT, 4326),
    address TEXT,
    speed_at_time DECIMAL(5,2),
    video_url VARCHAR(500),
    snapshot_url VARCHAR(500),
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by VARCHAR(100),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Geofence zones table
CREATE TABLE IF NOT EXISTS geospatial.geofence_zones (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    zone_type VARCHAR(50) NOT NULL,
    geometry GEOMETRY(POLYGON, 4326) NOT NULL,
    speed_limit DECIMAL(5,2),
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Routes table
CREATE TABLE IF NOT EXISTS geospatial.routes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    geometry GEOMETRY(LINESTRING, 4326) NOT NULL,
    waypoints JSONB,
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Truck routes assignment
CREATE TABLE IF NOT EXISTS geospatial.truck_routes (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER REFERENCES cctv.trucks(id) ON DELETE CASCADE,
    route_id INTEGER REFERENCES geospatial.routes(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    active BOOLEAN DEFAULT TRUE,
    UNIQUE(truck_id, route_id)
);

-- Location history table
CREATE TABLE IF NOT EXISTS geospatial.location_history (
    id SERIAL PRIMARY KEY,
    truck_id INTEGER REFERENCES cctv.trucks(id) ON DELETE CASCADE,
    location GEOMETRY(POINT, 4326) NOT NULL,
    speed DECIMAL(5,2),
    heading DECIMAL(5,2),
    altitude DECIMAL(8,2),
    accuracy DECIMAL(5,2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source VARCHAR(50) DEFAULT 'gps'
);

-- Alerts table
CREATE TABLE IF NOT EXISTS cctv.alerts (
    id SERIAL PRIMARY KEY,
    violation_id INTEGER REFERENCES cctv.violations(id) ON DELETE CASCADE,
    alert_type VARCHAR(50) NOT NULL,
    recipient VARCHAR(200) NOT NULL,
    channel VARCHAR(50) NOT NULL CHECK (channel IN ('email', 'sms', 'slack', 'teams', 'webhook')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'acknowledged')),
    message TEXT,
    metadata JSONB,
    sent_at TIMESTAMP WITH TIME ZONE,
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI models table
CREATE TABLE IF NOT EXISTS cctv.ai_models (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    version VARCHAR(20) NOT NULL,
    model_type VARCHAR(50) NOT NULL,
    file_path VARCHAR(500),
    accuracy DECIMAL(5,4),
    active BOOLEAN DEFAULT FALSE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(name, version)
);

-- System metrics table
CREATE TABLE IF NOT EXISTS monitoring.system_metrics (
    id SERIAL PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(12,4) NOT NULL,
    metric_type VARCHAR(20) DEFAULT 'gauge',
    labels JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance metrics table
CREATE TABLE IF NOT EXISTS monitoring.performance_metrics (
    id SERIAL PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL,
    endpoint VARCHAR(200),
    response_time_ms INTEGER,
    status_code INTEGER,
    error_count INTEGER DEFAULT 0,
    throughput_rps DECIMAL(8,2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trucks_status ON cctv.trucks(status);
CREATE INDEX IF NOT EXISTS idx_trucks_location ON cctv.trucks USING GIST(current_location);
CREATE INDEX IF NOT EXISTS idx_cameras_truck_id ON cctv.cameras(truck_id);
CREATE INDEX IF NOT EXISTS idx_cameras_status ON cctv.cameras(status);
CREATE INDEX IF NOT EXISTS idx_detections_truck_id ON cctv.detections(truck_id);
CREATE INDEX IF NOT EXISTS idx_detections_timestamp ON cctv.detections(timestamp);
CREATE INDEX IF NOT EXISTS idx_detections_object_type ON cctv.detections(object_type);
CREATE INDEX IF NOT EXISTS idx_detections_location ON cctv.detections USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_violations_truck_id ON cctv.violations(truck_id);
CREATE INDEX IF NOT EXISTS idx_violations_timestamp ON cctv.violations(created_at);
CREATE INDEX IF NOT EXISTS idx_violations_type ON cctv.violations(violation_type);
CREATE INDEX IF NOT EXISTS idx_violations_severity ON cctv.violations(severity);
CREATE INDEX IF NOT EXISTS idx_violations_resolved ON cctv.violations(resolved);
CREATE INDEX IF NOT EXISTS idx_violations_location ON cctv.violations USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_geofence_zones_geometry ON geospatial.geofence_zones USING GIST(geometry);
CREATE INDEX IF NOT EXISTS idx_geofence_zones_active ON geospatial.geofence_zones(active);
CREATE INDEX IF NOT EXISTS idx_routes_geometry ON geospatial.routes USING GIST(geometry);
CREATE INDEX IF NOT EXISTS idx_location_history_truck_id ON geospatial.location_history(truck_id);
CREATE INDEX IF NOT EXISTS idx_location_history_timestamp ON geospatial.location_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_location_history_location ON geospatial.location_history USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_alerts_violation_id ON cctv.alerts(violation_id);
CREATE INDEX IF NOT EXISTS idx_alerts_status ON cctv.alerts(status);
CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON cctv.alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_system_metrics_service ON monitoring.system_metrics(service_name);
CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON monitoring.system_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_service ON monitoring.performance_metrics(service_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON monitoring.performance_metrics(timestamp);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_trucks_updated_at BEFORE UPDATE ON cctv.trucks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cameras_updated_at BEFORE UPDATE ON cctv.cameras
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_violations_updated_at BEFORE UPDATE ON cctv.violations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_geofence_zones_updated_at BEFORE UPDATE ON geospatial.geofence_zones
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_routes_updated_at BEFORE UPDATE ON geospatial.routes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data
INSERT INTO cctv.trucks (truck_number, driver_name, license_plate, current_location) VALUES
('TR001', 'John Smith', 'ABC123', ST_SetSRID(ST_MakePoint(-122.4194, 37.7749), 4326)),
('TR002', 'Jane Doe', 'DEF456', ST_SetSRID(ST_MakePoint(-122.4094, 37.7849), 4326)),
('TR003', 'Bob Johnson', 'GHI789', ST_SetSRID(ST_MakePoint(-122.3994, 37.7949), 4326))
ON CONFLICT (truck_number) DO NOTHING;

-- Insert sample cameras
INSERT INTO cctv.cameras (truck_id, camera_position, stream_url) VALUES
(1, 'front', 'rtsp://admin:password@*************:554/stream1'),
(1, 'rear', 'rtsp://admin:password@*************:554/stream2'),
(1, 'left', 'rtsp://admin:password@*************:554/stream3'),
(1, 'right', 'rtsp://admin:password@*************:554/stream4'),
(2, 'front', 'rtsp://admin:password@*************:554/stream1'),
(2, 'rear', 'rtsp://admin:password@*************:554/stream2'),
(2, 'left', 'rtsp://admin:password@*************:554/stream3'),
(2, 'right', 'rtsp://admin:password@*************:554/stream4')
ON CONFLICT (truck_id, camera_position) DO NOTHING;

-- Insert sample geofence zones
INSERT INTO geospatial.geofence_zones (name, zone_type, geometry, speed_limit, description) VALUES
('Downtown Construction Zone', 'construction', 
 ST_SetSRID(ST_GeomFromText('POLYGON((-122.42 37.77, -122.41 37.77, -122.41 37.78, -122.42 37.78, -122.42 37.77))'), 4326),
 25.0, 'Active construction area - reduce speed'),
('School Zone Elementary', 'school',
 ST_SetSRID(ST_GeomFromText('POLYGON((-122.43 37.76, -122.42 37.76, -122.42 37.77, -122.43 37.77, -122.43 37.76))'), 4326),
 15.0, 'School zone - reduced speed during school hours'),
('Highway Section', 'highway',
 ST_SetSRID(ST_GeomFromText('POLYGON((-122.45 37.75, -122.40 37.75, -122.40 37.80, -122.45 37.80, -122.45 37.75))'), 4326),
 65.0, 'Highway section - higher speed limit')
ON CONFLICT DO NOTHING;

-- Insert sample routes
INSERT INTO geospatial.routes (name, geometry, waypoints, description) VALUES
('Downtown Route', 
 ST_SetSRID(ST_GeomFromText('LINESTRING(-122.42 37.77, -122.41 37.77, -122.40 37.78, -122.39 37.78)'), 4326),
 '[{"lat": 37.77, "lon": -122.42, "name": "Start"}, {"lat": 37.78, "lon": -122.39, "name": "End"}]',
 'Standard downtown delivery route'),
('Highway Route',
 ST_SetSRID(ST_GeomFromText('LINESTRING(-122.45 37.75, -122.40 37.75, -122.35 37.80, -122.30 37.80)'), 4326),
 '[{"lat": 37.75, "lon": -122.45, "name": "Highway Start"}, {"lat": 37.80, "lon": -122.30, "name": "Highway End"}]',
 'Highway route for long-distance deliveries')
ON CONFLICT DO NOTHING;

-- Grant all permissions to the application user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cctv TO mlops_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA monitoring TO mlops_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA geospatial TO mlops_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA cctv TO mlops_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA monitoring TO mlops_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA geospatial TO mlops_user;

-- Create views for common queries
CREATE OR REPLACE VIEW cctv.truck_summary AS
SELECT 
    t.id,
    t.truck_number,
    t.driver_name,
    t.license_plate,
    t.status,
    ST_AsText(t.current_location) as current_location_text,
    ST_X(t.current_location) as longitude,
    ST_Y(t.current_location) as latitude,
    COUNT(c.id) as camera_count,
    COUNT(CASE WHEN c.status = 'active' THEN 1 END) as active_cameras,
    t.last_seen,
    t.created_at
FROM cctv.trucks t
LEFT JOIN cctv.cameras c ON t.id = c.truck_id
GROUP BY t.id, t.truck_number, t.driver_name, t.license_plate, t.status, t.current_location, t.last_seen, t.created_at;

CREATE OR REPLACE VIEW cctv.recent_violations AS
SELECT 
    v.id,
    t.truck_number,
    t.driver_name,
    v.violation_type,
    v.description,
    v.severity,
    v.confidence,
    ST_AsText(v.location) as location_text,
    v.address,
    v.resolved,
    v.created_at
FROM cctv.violations v
JOIN cctv.trucks t ON v.truck_id = t.id
WHERE v.created_at >= NOW() - INTERVAL '24 hours'
ORDER BY v.created_at DESC;

COMMIT;