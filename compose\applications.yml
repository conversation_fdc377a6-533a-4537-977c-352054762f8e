version: '3.8'

networks:
  cctv-network:
    external: true

services:
  # API Gateway
  api-gateway:
    build:
      context: ../services/api-gateway
      dockerfile: Dockerfile
    container_name: cctv-api-gateway
    restart: unless-stopped
    environment:
      DATABASE_URL: *******************************************************/cctv_mlops
      REDIS_URL: redis://:redis_password@***********:6379/0
      MQTT_HOST: ***********
      MQTT_PORT: 1883
      MINIO_ENDPOINT: ***********:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      LOG_LEVEL: INFO
    ports:
      - "8000:8000"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
      - mosquitto
      - minio
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.localhost`)"
      - "traefik.http.services.api.loadbalancer.server.port=8000"

  # Stream Manager
  stream-manager:
    build:
      context: ../services/stream-manager
      dockerfile: Dockerfile
    container_name: cctv-stream-manager
    restart: unless-stopped
    environment:
      DATABASE_URL: *******************************************************/cctv_mlops
      REDIS_URL: redis://:redis_password@***********:6379/0
      FRIGATE_API_URL: http://***********:5000
      MQTT_HOST: ***********
      MQTT_PORT: 1883
      MAX_STREAMS: 400
      LOG_LEVEL: INFO
    ports:
      - "8001:8001"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - frigate
      - postgres
      - redis

  # AI Detection Service
  ai-detection:
    build:
      context: ../services/ai-detection
      dockerfile: Dockerfile
    container_name: cctv-ai-detection
    restart: unless-stopped
    environment:
      DATABASE_URL: *******************************************************/cctv_mlops
      REDIS_URL: redis://:redis_password@***********:6379/0
      MINIO_ENDPOINT: ***********:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      YOLO_MODEL_PATH: /app/models/yolo/best.pt
      CONFIDENCE_THRESHOLD: 0.5
      NMS_THRESHOLD: 0.4
      LOG_LEVEL: INFO
    ports:
      - "8002:8002"
    networks:
      cctv-network:
        ipv4_address: ***********
    volumes:
      - ../models:/app/models:ro
    depends_on:
      - postgres
      - redis
      - minio
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Agentic AI Service (Ollama-based)
  agentic-ai:
    build:
      context: ../services/agentic-ai
      dockerfile: Dockerfile
    container_name: cctv-agentic-ai
    restart: unless-stopped
    environment:
      DATABASE_URL: *******************************************************/cctv_mlops
      REDIS_URL: redis://:redis_password@***********:6379/0
      OLLAMA_API_URL: http://***********:11434
      MQTT_HOST: ***********
      MQTT_PORT: 1883
      LLM_MODEL: llama3.1:8b
      LOG_LEVEL: INFO
    ports:
      - "8003:8003"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - ollama
      - postgres
      - redis
      - mosquitto

  # Geofencing Service
  geofencing:
    build:
      context: ../services/geofencing
      dockerfile: Dockerfile
    container_name: cctv-geofencing
    restart: unless-stopped
    environment:
      DATABASE_URL: *******************************************************/cctv_mlops
      REDIS_URL: redis://:redis_password@***********:6379/0
      NOMINATIM_URL: http://***********:8080
      MQTT_HOST: ***********
      MQTT_PORT: 1883
      LOG_LEVEL: INFO
    ports:
      - "8004:8004"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - nominatim
      - postgres
      - redis

  # Alert Manager
  alert-manager:
    build:
      context: ../services/alert-manager
      dockerfile: Dockerfile
    container_name: cctv-alert-manager
    restart: unless-stopped
    environment:
      DATABASE_URL: *******************************************************/cctv_mlops
      REDIS_URL: redis://:redis_password@***********:6379/0
      MQTT_HOST: ***********
      MQTT_PORT: 1883
      SMTP_HOST: ***********
      SMTP_PORT: 1025
      SMTP_USER: ""
      SMTP_PASSWORD: ""
      WEBHOOK_ENDPOINTS: ""
      LOG_LEVEL: INFO
    ports:
      - "8005:8005"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - mailhog
      - postgres
      - redis
      - mosquitto

  # Streamlit Dashboard
  dashboard:
    build:
      context: ../services/dashboard
      dockerfile: Dockerfile
    container_name: cctv-dashboard
    restart: unless-stopped
    environment:
      DATABASE_URL: *******************************************************/cctv_mlops
      REDIS_URL: redis://:redis_password@***********:6379/0
      API_BASE_URL: http://***********:8000
      FRIGATE_API_URL: http://***********:5000
      NOMINATIM_URL: http://***********:8080
      STREAMLIT_SERVER_PORT: 8501
      STREAMLIT_SERVER_ADDRESS: 0.0.0.0
      LOG_LEVEL: INFO
    ports:
      - "8501:8501"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - api-gateway
      - postgres
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`dashboard.localhost`)"
      - "traefik.http.services.dashboard.loadbalancer.server.port=8501"

  # Monitoring Service
  monitoring:
    build:
      context: ../services/monitoring
      dockerfile: Dockerfile
    container_name: cctv-monitoring
    restart: unless-stopped
    environment:
      DATABASE_URL: *******************************************************/cctv_mlops
      REDIS_URL: redis://:redis_password@***********:6379/0
      PROMETHEUS_URL: http://***********:9090
      METRICS_PORT: 9091
      LOG_LEVEL: INFO
    ports:
      - "9091:9091"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
      - prometheus