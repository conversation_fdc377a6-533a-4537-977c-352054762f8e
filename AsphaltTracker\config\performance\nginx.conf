# NGINX Configuration for AsphaltTracker Enhanced
# Optimized for high-performance video streaming and AI processing

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Optimize worker connections
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 500m;  # For large video uploads
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Timeouts
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 300s;  # Extended for AI processing

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;
    limit_req_zone $binary_remote_addr zone=search:10m rate=5r/s;

    # Upstream servers
    upstream asphalt_tracker_app {
        least_conn;
        server asphalt-tracker-1:5000 max_fails=3 fail_timeout=30s;
        server asphalt-tracker-2:5000 max_fails=3 fail_timeout=30s;
        server asphalt-tracker-3:5000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream asphalt_tracker_ws {
        ip_hash;  # Sticky sessions for WebSocket
        server asphalt-tracker-1:5001 max_fails=3 fail_timeout=30s;
        server asphalt-tracker-2:5001 max_fails=3 fail_timeout=30s;
        server asphalt-tracker-3:5001 max_fails=3 fail_timeout=30s;
    }

    # Caching
    proxy_cache_path /var/cache/nginx/app levels=1:2 keys_zone=app_cache:10m max_size=1g inactive=60m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=2g inactive=24h use_temp_path=off;

    # Main server block
    server {
        listen 80;
        listen [::]:80;
        server_name asphalttracker.com www.asphalttracker.com;

        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    # HTTPS server
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name asphalttracker.com www.asphalttracker.com;

        # SSL configuration
        ssl_certificate /etc/ssl/certs/asphalttracker.crt;
        ssl_certificate_key /etc/ssl/private/asphalttracker.key;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;

        # Modern SSL configuration
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # HSTS
        add_header Strict-Transport-Security "max-age=63072000" always;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";

        # Root and index
        root /var/www/asphalttracker;
        index index.html;

        # Static files with caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status $upstream_cache_status;
            proxy_cache static_cache;
            proxy_cache_valid 200 24h;
            proxy_cache_use_stale error timeout invalid_header updating http_500 http_502 http_503 http_504;
        }

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://asphalt_tracker_app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # Caching for GET requests
            proxy_cache app_cache;
            proxy_cache_methods GET HEAD;
            proxy_cache_valid 200 5m;
            proxy_cache_key "$scheme$request_method$host$request_uri";
            add_header X-Cache-Status $upstream_cache_status;
        }

        # Video upload endpoint (special handling)
        location /api/v2/videos/upload {
            limit_req zone=upload burst=5 nodelay;
            
            proxy_pass http://asphalt_tracker_app;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for large uploads
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            client_body_timeout 300s;
        }

        # Search endpoint (rate limited)
        location /api/v2/search/ {
            limit_req zone=search burst=10 nodelay;
            
            proxy_pass http://asphalt_tracker_app;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket connections
        location /ws {
            proxy_pass http://asphalt_tracker_ws;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket specific timeouts
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
        }

        # Health check endpoint
        location /health {
            proxy_pass http://asphalt_tracker_app;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            access_log off;
        }

        # Metrics endpoint (restricted access)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            
            proxy_pass http://asphalt_tracker_app;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }

        # Video streaming (if serving videos directly)
        location /videos/ {
            alias /var/www/videos/;
            
            # Enable range requests for video streaming
            add_header Accept-Ranges bytes;
            
            # Security
            add_header X-Content-Type-Options nosniff;
            
            # Caching
            expires 1h;
            add_header Cache-Control "public";
        }

        # Frontend application
        location / {
            try_files $uri $uri/ /index.html;
            
            # Caching for HTML files
            location ~* \.html$ {
                expires 1h;
                add_header Cache-Control "public, must-revalidate";
            }
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /var/www/error;
        }
    }

    # Status server for monitoring
    server {
        listen 8080;
        server_name localhost;
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            deny all;
        }
    }
}
