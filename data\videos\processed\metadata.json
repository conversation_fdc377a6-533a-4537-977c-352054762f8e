[{"filename": "driving_sample_1.mp4", "duration": 120, "resolution": "1280x720", "frameRate": 25, "size": 15728640, "codec": "h264", "bitrate": 2000, "createdAt": "2024-01-01T00:00:00.000Z", "checksum": "sample_checksum_1"}, {"filename": "driving_sample_2.mp4", "duration": 135, "resolution": "1280x720", "frameRate": 25, "size": 17825792, "codec": "h264", "bitrate": 2000, "createdAt": "2024-01-01T00:00:00.000Z", "checksum": "sample_checksum_2"}, {"filename": "driving_sample_3.mp4", "duration": 110, "resolution": "1280x720", "frameRate": 25, "size": 14515200, "codec": "h264", "bitrate": 2000, "createdAt": "2024-01-01T00:00:00.000Z", "checksum": "sample_checksum_3"}, {"filename": "driving_sample_4.mp4", "duration": 125, "resolution": "1280x720", "frameRate": 25, "size": 16515072, "codec": "h264", "bitrate": 2000, "createdAt": "2024-01-01T00:00:00.000Z", "checksum": "sample_checksum_4"}, {"filename": "driving_sample_5.mp4", "duration": 140, "resolution": "1280x720", "frameRate": 25, "size": 18677760, "codec": "h264", "bitrate": 2000, "createdAt": "2024-01-01T00:00:00.000Z", "checksum": "sample_checksum_5"}, {"filename": "driving_sample_6.mp4", "duration": 115, "resolution": "1280x720", "frameRate": 25, "size": 15206400, "codec": "h264", "bitrate": 2000, "createdAt": "2024-01-01T00:00:00.000Z", "checksum": "sample_checksum_6"}]