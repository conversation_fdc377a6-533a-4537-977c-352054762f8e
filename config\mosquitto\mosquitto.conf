# Mosquitto MQTT Broker Configuration
# For Frigate and CCTV MLOps Communication

# Network Configuration
listener 1883 0.0.0.0
protocol mqtt

listener 9883 0.0.0.0
protocol websockets

# Authentication
allow_anonymous false
password_file /mosquitto/config/passwd

# Persistence
persistence true
persistence_location /mosquitto/data/
autosave_interval 1800

# Logging
log_dest file /mosquitto/log/mosquitto.log
log_type all
log_timestamp_format %Y-%m-%dT%H:%M:%S

# Connection limits
max_connections 1000
max_keepalive 65535

# Message limits
message_size_limit 268435456
max_inflight_messages 20
max_queued_messages 1000

# Security
require_certificate false
use_identity_as_username false

# Topic patterns for CCTV system
# frigate/+/+/events
# cctv/trucks/+/cameras/+/status
# cctv/alerts/+/violations
# cctv/geofencing/+/zones
# cctv/system/+/health

# ACL patterns (if needed)
# acl_file /mosquitto/config/acl.conf

# Bridge configuration (if connecting to external MQTT)
# connection bridge-01
# address external-mqtt.example.com:1883
# topic frigate/# out 0
# topic cctv/# out 0