# AsphaltTracker - Standalone Application

## Issue Resolution: "Repository is not a Restack project"

The error "Repository is not a Restack project" occurs when trying to run Restack commands in a directory that's not configured for Restack deployment. Here's how to resolve this:

## ✅ Solutions

### Option 1: Run Application Locally (Recommended)

Since this is a comprehensive logistics management platform with React frontend and Node.js backend, you should run it as a standard web application:

```bash
# Install dependencies (if npm issues persist, see troubleshooting below)
npm install

# Start development server
npm run dev

# Or use the workspace commands from the parent directory
cd ..
npm run dev:apps
```

### Option 2: Fix Dependency Issues

The npm installation errors are due to workspace configuration conflicts. To fix:

1. **Use Yarn instead of NPM** (recommended for workspaces):
```bash
yarn install
yarn dev
```

2. **Or clear all caches and reinstall**:
```bash
rm -rf node_modules package-lock.json
npm cache clean --force
npm install --legacy-peer-deps
```

3. **Or install dependencies from parent workspace**:
```bash
cd /mnt/c/Users/<USER>/computer-vision-mlops
npm install
npm run dev:apps
```

### Option 3: Deploy to Restack (If Desired)

If you want to deploy this to Restack cloud platform:

1. **Initialize Restack project** (from parent directory):
```bash
cd /mnt/c/Users/<USER>/computer-vision-mlops
restack init
```

2. **Configure restack.yaml** (already exists):
```bash
restack validate restack.yaml
restack deploy
```

## 🛠️ Current Application Features

The application includes:

### ✅ Completed Features
- **React Runtime Error Fixes**: Fixed date handling and WebSocket connections
- **Comprehensive Mock System**: 100 trucks, 400+ cameras with real-time simulation
- **GPS Tracking**: Real-time tracking with geo-fencing capabilities
- **Streaming Dashboard**: FFmpeg integration with RTSP endpoints  
- **AI Analytics**: Fraud detection, KPI scoring, predictive analytics
- **Real-time Monitoring**: WebSocket-based live updates
- **Multi-vendor Integration**: API adapters for various CCTV vendors

### 📊 Mock System APIs
Access the mock system endpoints:
```bash
# Start simulation
curl -X POST http://localhost:5000/api/mock/simulation/start

# Get system status
curl http://localhost:5000/api/mock/status

# Populate test data
curl -X POST http://localhost:5000/api/mock/populate/quick

# Streaming system health
curl http://localhost:5000/api/streams/health
```

## 🏃‍♂️ Quick Start

1. **From AsphaltTracker directory**:
```bash
npm run dev
```

2. **From parent workspace directory**:
```bash
npm run dev:apps
```

3. **Or use Docker** (if available):
```bash
npm run dev:docker
```

## 🔧 Troubleshooting

### NPM Version Issues
The "Invalid Version" errors are due to workspace configuration. Use one of these solutions:

1. **Use Yarn**: `yarn install && yarn dev`
2. **Use Legacy NPM**: `npm install --legacy-peer-deps`  
3. **Use Parent Workspace**: `cd .. && npm run dev:apps`

### Restack Errors
If you see "Repository is not a Restack project":
- You're trying to run `restack` commands in the wrong directory
- Run from parent directory: `/mnt/c/Users/<USER>/computer-vision-mlops`
- Or use standard npm/yarn commands instead

### Port Conflicts
Default port is 5000. If busy:
```bash
PORT=3000 npm run dev
```

## 📁 Application Structure

```
AsphaltTracker/
├── client/          # React frontend
├── server/          # Node.js backend
├── shared/          # Shared TypeScript schemas
├── scripts/         # Deployment and testing scripts
└── README.md       # This file
```

The application is production-ready with all React errors fixed, comprehensive mock data system implemented, and **full Restack AI framework integration**.

## 🤖 Restack AI Framework Integration

AsphaltTracker now uses the **Restack AI framework** (`@restackio/ai`) for workflow orchestration and activity management:

### ✅ Restack Features Implemented
- **Workflows**: Truck monitoring, GPS tracking, incident management
- **Activities**: 12+ activities for logistics operations 
- **Configuration**: Complete `restack.config.ts` and `restack.yaml`
- **Service Integration**: Seamless integration with existing Express.js API
- **Health Checks**: Restack-aware health endpoints for cloud deployment

### 🔧 Restack Commands
```bash
# Validate Restack integration
npm run restack:validate

# Check Restack service status  
npm run restack:check

# Test complete Restack compatibility
npm run test:restack
```

### 🚀 Restack Workflows Available
- `truckMonitoringWorkflow` - Real-time truck monitoring
- `gpsTrackingWorkflow` - GPS tracking with geofencing
- `incidentManagementWorkflow` - AI-powered incident detection
- `batchTruckMonitoringWorkflow` - Fleet-wide monitoring
- `fleetGpsTrackingWorkflow` - Multi-truck GPS tracking

### 📡 Restack API Endpoints
- `POST /api/restack/workflows/truck-monitoring` - Start truck monitoring
- `GET /api/restack/status` - Get Restack service status
- `GET /health` - Enhanced health check with Restack info