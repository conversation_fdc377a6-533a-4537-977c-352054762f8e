# PostgreSQL Configuration for AsphaltTracker Enhanced
# Optimized for video metadata, analytics, and high-throughput operations

# Connection Settings
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings (adjust based on available RAM)
shared_buffers = 4GB                    # 25% of total RAM
effective_cache_size = 12GB             # 75% of total RAM
work_mem = 64MB                         # For complex queries
maintenance_work_mem = 1GB              # For maintenance operations
dynamic_shared_memory_type = posix

# WAL (Write-Ahead Logging) Settings
wal_level = replica
wal_buffers = 64MB
max_wal_size = 4GB
min_wal_size = 1GB
checkpoint_completion_target = 0.9
checkpoint_timeout = 15min
archive_mode = on
archive_command = 'cp %p /var/lib/postgresql/archive/%f'

# Query Planner Settings
random_page_cost = 1.1                  # For SSD storage
effective_io_concurrency = 200          # For SSD storage
seq_page_cost = 1.0
cpu_tuple_cost = 0.01
cpu_index_tuple_cost = 0.005
cpu_operator_cost = 0.0025

# Parallel Query Settings
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_parallel_maintenance_workers = 4
parallel_tuple_cost = 0.1
parallel_setup_cost = 1000.0

# Background Writer Settings
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0
bgwriter_flush_after = 512kB

# Autovacuum Settings (important for high-write workloads)
autovacuum = on
autovacuum_max_workers = 6
autovacuum_naptime = 30s
autovacuum_vacuum_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_threshold = 50
autovacuum_analyze_scale_factor = 0.05
autovacuum_vacuum_cost_delay = 10ms
autovacuum_vacuum_cost_limit = 1000

# Logging Settings
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_truncate_on_rotation = on
log_min_duration_statement = 1000       # Log slow queries (1 second)
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0
log_autovacuum_min_duration = 0

# Statistics Settings
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all
stats_temp_directory = '/var/run/postgresql/stats_temp'

# Lock Management
deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64

# Error Handling
restart_after_crash = on
exit_on_error = off

# Locale and Formatting
datestyle = 'iso, mdy'
timezone = 'UTC'
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'
default_text_search_config = 'pg_catalog.english'

# Replication Settings (for read replicas)
hot_standby = on
max_wal_senders = 3
wal_keep_segments = 32
hot_standby_feedback = on

# Extensions for AsphaltTracker
shared_preload_libraries = 'pg_stat_statements,auto_explain'

# pg_stat_statements settings
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = on
pg_stat_statements.save = on

# auto_explain settings for query optimization
auto_explain.log_min_duration = 5000    # Log explain plans for queries > 5s
auto_explain.log_analyze = on
auto_explain.log_buffers = on
auto_explain.log_timing = on
auto_explain.log_triggers = on
auto_explain.log_verbose = on
auto_explain.log_nested_statements = on

# Custom settings for AsphaltTracker workloads

# JSON/JSONB optimization (for video metadata)
gin_pending_list_limit = 4MB

# Full-text search optimization
default_text_search_config = 'pg_catalog.english'

# Time zone for timestamp operations
timezone = 'UTC'

# Statement timeout (prevent runaway queries)
statement_timeout = 300000              # 5 minutes

# Lock timeout
lock_timeout = 30000                    # 30 seconds

# Idle in transaction timeout
idle_in_transaction_session_timeout = 600000  # 10 minutes

# TCP settings
tcp_keepalives_idle = 600
tcp_keepalives_interval = 30
tcp_keepalives_count = 3

# SSL Settings (if using SSL)
ssl = on
ssl_cert_file = '/etc/ssl/certs/server.crt'
ssl_key_file = '/etc/ssl/private/server.key'
ssl_ca_file = '/etc/ssl/certs/ca.crt'
ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'
ssl_prefer_server_ciphers = on

# Huge pages (if available)
huge_pages = try

# Performance monitoring
log_statement_stats = off
log_parser_stats = off
log_planner_stats = off
log_executor_stats = off

# Custom maintenance settings
vacuum_cost_delay = 10ms
vacuum_cost_page_hit = 1
vacuum_cost_page_miss = 10
vacuum_cost_page_dirty = 20
vacuum_cost_limit = 2000

# Specific optimizations for AsphaltTracker tables

# Videos table optimization
# CREATE INDEX CONCURRENTLY idx_videos_status ON videos(status);
# CREATE INDEX CONCURRENTLY idx_videos_created_at ON videos(created_at);
# CREATE INDEX CONCURRENTLY idx_videos_camera_id ON videos(camera_id);

# Activities table optimization  
# CREATE INDEX CONCURRENTLY idx_activities_timestamp ON activities(timestamp);
# CREATE INDEX CONCURRENTLY idx_activities_camera_id ON activities(camera_id);
# CREATE INDEX CONCURRENTLY idx_activities_type ON activities(type);

# Alerts table optimization
# CREATE INDEX CONCURRENTLY idx_alerts_status ON alerts(status);
# CREATE INDEX CONCURRENTLY idx_alerts_severity ON alerts(severity);
# CREATE INDEX CONCURRENTLY idx_alerts_created_at ON alerts(created_at);

# JSONB indexes for metadata
# CREATE INDEX CONCURRENTLY idx_videos_analysis_gin ON videos USING gin(analysis);
# CREATE INDEX CONCURRENTLY idx_activities_metadata_gin ON activities USING gin(metadata);

# Partial indexes for active records
# CREATE INDEX CONCURRENTLY idx_alerts_active ON alerts(created_at) WHERE status = 'active';
# CREATE INDEX CONCURRENTLY idx_videos_processing ON videos(created_at) WHERE status = 'processing';
