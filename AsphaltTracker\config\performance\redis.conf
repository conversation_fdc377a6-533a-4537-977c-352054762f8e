# Redis Configuration for AsphaltTracker Enhanced
# Optimized for real-time data processing and caching

# Network and connection settings
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 300
tcp-keepalive 300

# General settings
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Memory management
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence settings for real-time data
save 900 1
save 300 10
save 60 10000

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF (Append Only File) for durability
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua scripting
lua-time-limit 5000

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency monitoring
latency-monitor-threshold 100

# Event notification for real-time features
notify-keyspace-events "Ex"

# Client settings
maxclients 10000

# Security
requirepass your_redis_password_here
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d2e7f1"

# Performance optimizations for AsphaltTracker
# Optimize for real-time activity tracking
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000

# Stream settings for real-time data
stream-node-max-bytes 4096
stream-node-max-entries 100

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# TCP settings
tcp-keepalive 300
tcp-backlog 511

# Threading (Redis 6.0+)
io-threads 4
io-threads-do-reads yes

# Memory usage optimization
activerehashing yes
client-query-buffer-limit 1gb
proto-max-bulk-len 512mb

# Replication settings (if using Redis cluster)
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# Cluster settings (if using Redis cluster)
# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-replica-validity-factor 10
# cluster-migration-barrier 1
# cluster-require-full-coverage yes

# Module loading (if using Redis modules)
# loadmodule /path/to/module.so

# Custom configurations for AsphaltTracker use cases

# Activity tracking keys TTL (1 hour)
# SET activity:* EX 3600

# Video processing queue settings
# LPUSH video_processing_queue
# BRPOP video_processing_queue 0

# Real-time alerts (30 minutes TTL)
# SET alert:* EX 1800

# Camera status cache (5 minutes TTL)
# SET camera:status:* EX 300

# AI model cache (1 day TTL)
# SET ai:model:* EX 86400

# Session management (24 hours TTL)
# SET session:* EX 86400

# Search results cache (15 minutes TTL)
# SET search:* EX 900
