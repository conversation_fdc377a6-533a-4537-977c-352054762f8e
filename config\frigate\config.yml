# Frigate Open-Source CCTV Configuration
# Optimized for 400 cameras (100 trucks × 4 cameras)

mqtt:
  enabled: true
  host: mosquitto
  port: 1883
  topic_prefix: frigate
  user: frigate
  password: frigate_password
  stats_interval: 60

database:
  path: /config/frigate.db

model:
  # Use OpenVINO for CPU inference or TensorRT for GPU
  path: /config/model_cache/yolo/
  input_tensor: nchw
  input_pixel_format: rgb
  width: 320
  height: 320

detectors:
  # CPU detector for basic object detection
  cpu1:
    type: cpu
    num_threads: 4
  
  # GPU detector for advanced processing (if NVIDIA GPU available)
  gpu1:
    type: tensorrt
    device: 0

# Global detection configuration
detect:
  enabled: true
  width: 1280
  height: 720
  fps: 5
  max_disappeared: 25

# Global object configuration
objects:
  track:
    - person
    - car
    - truck
    - bicycle
    - motorcycle
    - bus
    - cell_phone
    - backpack
    - handbag
    - suitcase
  filters:
    person:
      min_area: 5000
      max_area: 100000
      min_score: 0.5
      threshold: 0.7
    car:
      min_area: 15000
      max_area: 200000
      min_score: 0.5
      threshold: 0.7
    cell_phone:
      min_area: 500
      max_area: 10000
      min_score: 0.6
      threshold: 0.8

# Record configuration
record:
  enabled: true
  retain:
    days: 7
    mode: motion
  events:
    pre_capture: 5
    post_capture: 5
    objects:
      - person
      - car
      - cell_phone
    retain:
      default: 7
      mode: active_objects

# Snapshots configuration
snapshots:
  enabled: true
  timestamp: true
  bounding_box: true
  crop: false
  retain:
    default: 7

# Live stream configuration
live:
  height: 720
  quality: 8

# Go2RTC configuration for stream management
go2rtc:
  streams:
    # Example streams - will be populated by stream manager
    truck_001_front: "rtsp://admin:password@192.168.1.100:554/stream1"
    truck_001_rear: "rtsp://admin:password@192.168.1.100:554/stream2"
    truck_001_left: "rtsp://admin:password@192.168.1.100:554/stream3"
    truck_001_right: "rtsp://admin:password@192.168.1.100:554/stream4"

# Cameras configuration
cameras:
  # Truck 001 - Front Camera
  truck_001_front:
    enabled: true
    ffmpeg:
      inputs:
        - path: rtsp://admin:password@192.168.1.100:554/stream1
          input_args: preset-rtsp-restream
          roles:
            - record
            - detect
    detect:
      enabled: true
      width: 1280
      height: 720
      fps: 5
    zones:
      driver_area:
        coordinates: 300,200,980,200,980,520,300,520
        objects:
          - person
          - cell_phone
        filters:
          person:
            min_area: 5000
            max_area: 50000
          cell_phone:
            min_area: 500
            max_area: 5000
      cabin_area:
        coordinates: 0,0,1280,720
        objects:
          - person
    motion:
      mask:
        - 0,0,300,0,300,720,0,720
    record:
      enabled: true
      retain:
        days: 7
        mode: motion
      events:
        pre_capture: 5
        post_capture: 5
    snapshots:
      enabled: true

  # Truck 001 - Rear Camera
  truck_001_rear:
    enabled: true
    ffmpeg:
      inputs:
        - path: rtsp://admin:password@192.168.1.100:554/stream2
          input_args: preset-rtsp-restream
          roles:
            - record
            - detect
    detect:
      enabled: true
      width: 1280
      height: 720
      fps: 5
    zones:
      loading_area:
        coordinates: 200,300,1080,300,1080,720,200,720
        objects:
          - person
          - car
          - truck
    record:
      enabled: true
    snapshots:
      enabled: true

  # Truck 001 - Left Side Camera
  truck_001_left:
    enabled: true
    ffmpeg:
      inputs:
        - path: rtsp://admin:password@192.168.1.100:554/stream3
          input_args: preset-rtsp-restream
          roles:
            - record
            - detect
    detect:
      enabled: true
      width: 1280
      height: 720
      fps: 5
    zones:
      side_area:
        coordinates: 0,200,1280,200,1280,720,0,720
        objects:
          - person
          - car
          - bicycle
          - motorcycle
    record:
      enabled: true
    snapshots:
      enabled: true

  # Truck 001 - Right Side Camera
  truck_001_right:
    enabled: true
    ffmpeg:
      inputs:
        - path: rtsp://admin:password@192.168.1.100:554/stream4
          input_args: preset-rtsp-restream
          roles:
            - record
            - detect
    detect:
      enabled: true
      width: 1280
      height: 720
      fps: 5
    zones:
      side_area:
        coordinates: 0,200,1280,200,1280,720,0,720
        objects:
          - person
          - car
          - bicycle
          - motorcycle
    record:
      enabled: true
    snapshots:
      enabled: true

# Additional camera configurations will be auto-generated
# Template for remaining 99 trucks (trucks 002-100)
# Each truck follows the same pattern:
# - truck_XXX_front
# - truck_XXX_rear  
# - truck_XXX_left
# - truck_XXX_right

# Notification configuration
notifications:
  general:
    - name: truck_violations
      filters:
        - zones:
            - driver_area
        - objects:
            - cell_phone
        - camera:
            - truck_.*_front
  mqtt:
    - name: all_events
      filters: {}
      message: >-
        A {label} was detected in {camera_name} at {timestamp}
      retain: false
      qos: 1

# Review configuration
review:
  alerts:
    required_zones: []
    labels:
      - person
      - cell_phone
    # Alert retention
    retain:
      default: 7

# UI configuration
ui:
  live_mode: mse
  timezone: UTC
  use_experimental: false
  time_format: 24hour
  date_style: us

# Logging configuration
logger:
  default: info
  logs:
    frigate.video: warning
    frigate.motion: warning
    frigate.object_processing: warning