# Render.com Dockerfile for Streamlit Dashboard

FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PORT=8501

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY dashboard/requirements-render.txt requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY dashboard/ .

# Create non-root user
RUN useradd -m -u 1000 streamlit && chown -R streamlit:streamlit /app
USER streamlit

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/_stcore/health || exit 1

# Expose port
EXPOSE ${PORT}

# Create Streamlit config
RUN mkdir -p ~/.streamlit
RUN echo '[server]\nport = '${PORT}'\naddress = "0.0.0.0"\nheadless = true\nenableCORS = false\nenableXsrfProtection = false\n\n[theme]\nbase = "light"\nprimaryColor = "#0078d4"\nbackgroundColor = "#ffffff"\nsecondaryBackgroundColor = "#f0f2f6"\ntextColor = "#262730"' > ~/.streamlit/config.toml

# Run Streamlit
CMD streamlit run main.py --server.port=${PORT} --server.address=0.0.0.0