# Prometheus Configuration for CCTV MLOps Monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'cctv-mlops'
    environment: 'production'

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alert-manager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets:
        - 'node-exporter:9100'
    scrape_interval: 5s
    metrics_path: /metrics

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s

  # PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s

  # MinIO metrics
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: /minio/v2/metrics/cluster
    scrape_interval: 15s

  # Frigate metrics
  - job_name: 'frigate'
    static_configs:
      - targets: ['frigate:5000']
    metrics_path: /api/stats
    scrape_interval: 30s

  # API Gateway metrics
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8000']
    metrics_path: /metrics
    scrape_interval: 15s

  # Stream Manager metrics
  - job_name: 'stream-manager'
    static_configs:
      - targets: ['stream-manager:8001']
    metrics_path: /metrics
    scrape_interval: 15s

  # AI Detection Service metrics
  - job_name: 'ai-detection'
    static_configs:
      - targets: ['ai-detection:8002']
    metrics_path: /metrics
    scrape_interval: 15s

  # Agentic AI Service metrics
  - job_name: 'agentic-ai'
    static_configs:
      - targets: ['agentic-ai:8003']
    metrics_path: /metrics
    scrape_interval: 15s

  # Geofencing Service metrics
  - job_name: 'geofencing'
    static_configs:
      - targets: ['geofencing:8004']
    metrics_path: /metrics
    scrape_interval: 15s

  # Alert Manager metrics
  - job_name: 'alert-manager'
    static_configs:
      - targets: ['alert-manager:8005']
    metrics_path: /metrics
    scrape_interval: 15s

  # Dashboard metrics
  - job_name: 'dashboard'
    static_configs:
      - targets: ['dashboard:8501']
    metrics_path: /metrics
    scrape_interval: 30s

  # Monitoring Service metrics
  - job_name: 'monitoring'
    static_configs:
      - targets: ['monitoring:9091']
    metrics_path: /metrics
    scrape_interval: 15s

  # Ollama LLM metrics
  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
    metrics_path: /metrics
    scrape_interval: 30s

  # MQTT Mosquitto metrics
  - job_name: 'mosquitto'
    static_configs:
      - targets: ['mosquitto-exporter:9234']
    scrape_interval: 15s

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

  # Traefik metrics
  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik:8080']
    metrics_path: /metrics
    scrape_interval: 15s

# Remote write configuration (optional for long-term storage)
# remote_write:
#   - url: "http://thanos-receive:19291/api/v1/receive"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500