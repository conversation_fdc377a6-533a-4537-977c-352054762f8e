# AsphaltTracker Enhanced Environment Variables - Restack.io Compatible
# Copy this file to .env and update with your actual values

# Application Settings
NODE_ENV=production
PORT=5000
HOST=0.0.0.0
WS_PORT=5001
LOG_LEVEL=info

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
CORS_ORIGIN=*

# Database (Automatically provided by Restack)
DATABASE_URL=************************************/asphalt_tracker
REDIS_URL=redis://host:6379

# NVIDIA API Configuration (Required for AI features)
NVIDIA_API_KEY=your_nvidia_api_key_here
NVIDIA_API_BASE_URL=https://api.nvidia.com/v1

# VSS Model Configuration
VSS_USE_LOCAL=false
VLM_MODEL=nvidia/vila
VLM_ENDPOINT=https://api.nvidia.com/v1/vlm/nvidia/vila
LLM_MODEL=meta/llama-3.1-70b-instruct
LLM_ENDPOINT=https://api.nvidia.com/v1/chat/completions
EMBEDDING_MODEL=nvidia/llama-3_2-nv-embedqa-1b-v2
EMBEDDING_ENDPOINT=https://api.nvidia.com/v1/embeddings
RERANKER_MODEL=nvidia/llama-3_2-nv-rerankqa-1b-v2
RERANKER_ENDPOINT=https://api.nvidia.com/v1/ranking
ASR_MODEL=nvidia/parakeet-ctc-0_6b-asr
ASR_ENDPOINT=https://api.nvidia.com/v1/asr

# Local VSS Engine (if using local deployment)
VSS_ENGINE_HOST=localhost
VSS_ENGINE_PORT=8000

# Storage Configuration (Enhanced)
STORAGE_URL=s3://bucket-name
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
VIDEO_STORAGE_PATH=./uploads/videos
PROCESSED_STORAGE_PATH=./data/processed
MAX_VIDEO_SIZE=500MB
VIDEO_RETENTION_DAYS=90
CLOUD_STORAGE_ENABLED=false
CLOUD_PROVIDER=s3
STORAGE_BUCKET=asphalt-tracker-videos

# Database Configuration (Enhanced)
VECTOR_DB_TYPE=chroma
VECTOR_DB_HOST=localhost
VECTOR_DB_PORT=8005
GRAPH_DB_TYPE=neo4j
GRAPH_DB_HOST=localhost
GRAPH_DB_PORT=7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_neo4j_password
TSDB_TYPE=influxdb
TSDB_HOST=localhost
TSDB_PORT=8086

# Security (Enhanced)
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key
VSS_AUTH_ENABLED=true
VSS_API_KEY=your_vss_api_key

# Enhanced Features
MAX_CAMERA_STREAMS=400
ENABLE_REAL_TIME_ALERTS=true
ENABLE_ANALYTICS=true
ENABLE_GEOFENCING=true
DEBUG_MODE=false
FEATURE_REAL_TIME_PROCESSING=true
FEATURE_BATCH_PROCESSING=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_SAFETY_MONITORING=true
FEATURE_PROGRESS_TRACKING=true
FEATURE_QUALITY_ASSESSMENT=true

# API Configuration (Enhanced)
API_RATE_LIMIT=1000
TRUST_PROXY=true
MAX_CONCURRENT_PROCESSING=10
QUEUE_CONCURRENCY=5

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# File Upload (Enhanced)
MAX_FILE_SIZE=500MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,mp4,avi,mov,mkv,webm

# AI Configuration
AI_CONFIDENCE_THRESHOLD=0.75
AI_BATCH_SIZE=10
AI_MAX_WAIT_TIME=5000
AI_FALLBACK_STRATEGY=local

# Safety Thresholds
SAFETY_PPE_COMPLIANCE=0.95
SAFETY_PROXIMITY_DISTANCE=5
SAFETY_SPEED_LIMIT=25

# Progress Thresholds
PROGRESS_SCHEDULE_VARIANCE=0.1
PROGRESS_QUALITY_THRESHOLD=0.8
PROGRESS_PRODUCTIVITY_DROP=0.2

# Equipment Thresholds
EQUIPMENT_UTILIZATION_THRESHOLD=0.7
EQUIPMENT_MAINTENANCE_OVERDUE=7
EQUIPMENT_PERFORMANCE_DEGRADATION=0.2

# Alert Configuration
ALERT_DUPLICATE_WINDOW=300
ALERT_SIMILARITY_THRESHOLD=0.8
ALERT_MAX_PER_HOUR=100

# Notification Services
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********
FIREBASE_SERVER_KEY=your_firebase_server_key
FIREBASE_PROJECT_ID=your_firebase_project_id
SLACK_ENABLED=false
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Analytics (Enhanced)
ANALYTICS_RETENTION_DAYS=90
ENABLE_PREDICTIVE_ANALYTICS=true
PERFORMANCE_MONITORING=true
ERROR_TRACKING=true
USAGE_ANALYTICS=true

# Monitoring (Enhanced)
ENABLE_METRICS=true
METRICS_PORT=9090
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
GRAFANA_ENABLED=true
GRAFANA_PORT=3001
HEALTH_CHECK_INTERVAL=60s

# CCTV Integration
CCTV_DEFAULT_PROTOCOL=rtsp
CCTV_TIMEOUT=30000
CCTV_STREAM_RESOLUTION=1920x1080
CCTV_FRAME_RATE=30

# GPS Integration
GPS_UPDATE_INTERVAL=5000
GPS_ACCURACY=high
GPS_GEOFENCING=true

# Backup and Compliance
BACKUP_ENABLED=true
BACKUP_INTERVAL=24h
BACKUP_RETENTION=30d
AUDIT_LOGGING=true
COMPLIANCE_REPORTING=true
DATA_RETENTION_POLICY=365d

# Third-party APIs
WEATHER_API_KEY=your_weather_api_key
MAPS_API_KEY=your_maps_api_key

# Development/Production Flags
MOCK_DATA=false
HOT_RELOAD=false
SSL_ENABLED=false