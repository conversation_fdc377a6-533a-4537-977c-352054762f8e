# AsphaltTracker Environment Variables - Restack.io Compatible

# Application
NODE_ENV=production
PORT=5000
HOST=0.0.0.0
LOG_LEVEL=INFO

# Database (Automatically provided by Restack)
DATABASE_URL=************************************/asphalt_tracker
REDIS_URL=redis://host:6379

# Storage (Automatically provided by Restack)
STORAGE_URL=s3://bucket-name
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1

# Security
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Features
MAX_CAMERA_STREAMS=400
ENABLE_REAL_TIME_ALERTS=true
ENABLE_ANALYTICS=true
ENABLE_GEOFENCING=true
DEBUG_MODE=false

# API Configuration
API_RATE_LIMIT=1000
CORS_ORIGIN=*
TRUST_PROXY=true

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# File Upload
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,mp4,avi,mov

# Analytics
ANALYTICS_RETENTION_DAYS=30
ENABLE_PREDICTIVE_ANALYTICS=true

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090