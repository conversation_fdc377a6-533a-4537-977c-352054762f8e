{
  "name": "asphalt-tracker",
  "version": "1.0.0",
<<<<<<< Updated upstream
  "type": "module",
=======
  "private": true,
  "description": "Real-time Computer Vision MLOps for Multi-Source CCTV Object Detection",
  "main": "index.js",
  "workspaces": [
    "AsphaltTracker",
    "frontend"
  ],
  "scripts": {
    "deploy": "bash scripts/deploy-restack.sh",
    "deploy:staging": "bash scripts/deploy-restack.sh --config restack.staging.yaml",
    "validate": "restack validate restack.yaml",
    "status": "restack status",
    "logs": "restack logs",
    "dev": "turbo run dev --parallel",
    "dev:apps": "turbo run dev --filter=AsphaltTracker --filter=frontend --parallel",
    "dev:docker": "docker-compose -f compose/infrastructure.yml -f compose/applications.yml up -d",
    "dev:down": "docker-compose -f compose/infrastructure.yml -f compose/applications.yml down",
    "build": "turbo run build",
    "start": "turbo run start --parallel",
    "test": "turbo run test",
    "test:services": "pytest services/*/tests/",
    "test:integration": "pytest tests/integration/",
    "lint": "turbo run lint",
    "format": "black services/",
    "docs:build": "mkdocs build",
    "docs:serve": "mkdocs serve",
    "backup": "restack job run database-backup",
    "restore": "bash scripts/restore-backup.sh",
    "scale:up": "restack scale --service frigate --instances 2",
    "scale:down": "restack scale --service frigate --instances 1",
    "setup:local": "bash scripts/setup-local-dev.sh",
    "cleanup": "restack job run video-cleanup"
  },
  "repository": {
    "type": "git",
    "url": "git+https://github.com/your-org/computer-vision-mlops.git"
  },
  "keywords": [
    "computer-vision",
    "mlops",
    "cctv",
    "object-detection",
    "frigate",
    "restack",
    "open-source"
  ],
  "author": "Your Engineering Team",
>>>>>>> Stashed changes
  "license": "MIT",
  "scripts": {
    "dev": "cross-env NODE_ENV=development node --import tsx/esm server/index.ts",
    "dev:tsx": "cross-env NODE_ENV=development tsx server/index.ts",
    "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist --target=node18",
    "start": "cross-env NODE_ENV=production node dist/index.js",
    "start:dev": "cross-env NODE_ENV=development node --import tsx/esm server/index.ts",
    "check": "tsc",
    "db:push": "drizzle-kit push",
    "db:migrate": "drizzle-kit migrate",
    "health": "curl -f http://localhost:5000/health || exit 1",
    "mock:populate": "curl -s -X POST http://localhost:5000/api/mock/populate/quick",
    "mock:simulate": "curl -s -X POST http://localhost:5000/api/mock/simulation/start",
    "mock:status": "curl -s http://localhost:5000/api/mock/status",
    "streaming:health": "curl -s http://localhost:5000/api/streams/health",
    "streaming:status": "curl -s http://localhost:5000/api/streams/status",
    "streaming:init": "curl -s -X POST http://localhost:5000/api/streams/initialize",
    "restack:check": "curl -s http://localhost:5000/api/restack/status",
    "restack:health": "curl -f http://localhost:5000/health | grep '@restackio/ai' || exit 1",
    "restack:validate": "node -e 'console.log(\"✅ Restack AI dependency found:\", require(\"./package.json\").dependencies[\"@restackio/ai\"])'",
    "test:restack": "npm run restack:validate && npm run health && npm run restack:check"
  },
  "dependencies": {
    "@restackio/ai": "^0.0.126",
    "@hookform/resolvers": "^3.10.0",
    "@neondatabase/serverless": "^0.10.4",
    "@radix-ui/react-accordion": "^1.2.4",
    "@radix-ui/react-alert-dialog": "^1.1.7",
    "@radix-ui/react-aspect-ratio": "^1.1.3",
    "@radix-ui/react-avatar": "^1.1.4",
    "@radix-ui/react-checkbox": "^1.1.5",
    "@radix-ui/react-collapsible": "^1.1.4",
    "@radix-ui/react-context-menu": "^2.2.7",
    "@radix-ui/react-dialog": "^1.1.7",
    "@radix-ui/react-dropdown-menu": "^2.1.7",
    "@radix-ui/react-hover-card": "^1.1.7",
    "@radix-ui/react-label": "^2.1.3",
    "@radix-ui/react-menubar": "^1.1.7",
    "@radix-ui/react-navigation-menu": "^1.2.6",
    "@radix-ui/react-popover": "^1.1.7",
    "@radix-ui/react-progress": "^1.1.3",
    "@radix-ui/react-radio-group": "^1.2.4",
    "@radix-ui/react-scroll-area": "^1.2.4",
    "@radix-ui/react-select": "^2.1.7",
    "@radix-ui/react-separator": "^1.1.3",
    "@radix-ui/react-slider": "^1.2.4",
    "@radix-ui/react-slot": "^1.2.0",
    "@radix-ui/react-switch": "^1.1.4",
    "@radix-ui/react-tabs": "^1.1.4",
    "@radix-ui/react-toast": "^1.2.7",
    "@radix-ui/react-toggle": "^1.1.3",
    "@radix-ui/react-toggle-group": "^1.1.3",
    "@radix-ui/react-tooltip": "^1.2.0",
    "@tanstack/react-query": "^5.60.5",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cmdk": "^1.1.1",
    "connect-pg-simple": "^10.0.0",
    "date-fns": "^3.6.0",
    "drizzle-orm": "^0.39.1",
    "drizzle-zod": "^0.7.0",
    "embla-carousel-react": "^8.6.0",
    "express": "^4.21.2",
    "express-session": "^1.18.1",
    "framer-motion": "^11.13.1",
    "input-otp": "^1.4.2",
    "lucide-react": "^0.453.0",
    "memorystore": "^1.6.7",
    "multer": "^1.4.5-lts.1",
    "next-themes": "^0.4.6",
    "passport": "^0.7.0",
    "passport-local": "^1.0.0",
    "react": "^18.3.1",
    "react-day-picker": "^8.10.1",
    "react-dom": "^18.3.1",
    "react-hook-form": "^7.55.0",
    "react-icons": "^5.4.0",
    "react-resizable-panels": "^2.1.7",
    "recharts": "^2.15.2",
    "tailwind-merge": "^2.6.0",
    "tailwindcss-animate": "^1.0.7",
    "vaul": "^1.1.2",
    "wouter": "^3.3.5",
    "ws": "^8.18.0",
    "zod": "^3.24.2",
    "zod-validation-error": "^3.4.0"
  },
  "devDependencies": {
<<<<<<< Updated upstream
    "@tailwindcss/typography": "^0.5.15",
    "@types/connect-pg-simple": "^7.0.3",
    "@types/express": "^4.17.21",
    "@types/express-session": "^1.18.0",
    "@types/multer": "^1.4.12",
    "@types/node": "^20.16.11",
    "@types/passport": "^1.0.16",
    "@types/passport-local": "^1.0.38",
    "@types/react": "^18.3.11",
    "@types/react-dom": "^18.3.1",
    "@types/ws": "^8.5.13",
    "@vitejs/plugin-react": "^4.3.2",
    "autoprefixer": "^10.4.20",
    "cross-env": "^7.0.3",
    "drizzle-kit": "^0.30.4",
    "esbuild": "^0.25.0",
    "esbuild-wasm": "^0.25.0",
    "postcss": "^8.4.47",
    "tailwindcss": "^3.4.17",
    "tsx": "^4.19.1",
    "typescript": "^5.6.3",
    "vite": "^5.4.19"
  }
=======
    "eslint": "^8.55.0",
    "prettier": "^3.1.0",
    "turbo": "^1.13.0"
  },
  "engines": {
    "node": ">=16.0.0",
    "npm": ">=8.0.0"
  },
  "restack": {
    "config": "restack.yaml",
    "project": "asphalt-cctv-mlops",
    "environments": {
      "production": "restack.yaml",
      "staging": "restack.staging.yaml",
      "development": "restack.dev.yaml"
    }
  },
  "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"
>>>>>>> Stashed changes
}
