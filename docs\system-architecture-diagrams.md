# 🏗️ System Architecture & Data Flow Diagrams

## System Architecture Overview

```mermaid
---
config:
  layout: elk
---
flowchart TB
 subgraph subGraph0["External Systems"]
        T1["Truck 1 - 4 Cameras"]
        T2["Truck 2 - 4 Cameras"]
        T100["Truck 100 - 4 Cameras"]
        GPS["GPS Tracking System"]
        API["CCTV API Gateway"]
  end
 subgraph subGraph1["AKS Cluster"]
        F["Frigate CCTV Engine"]
        AI["Agentic AI Service"]
        GF["Geo-fencing Service"]
        AL["Alerting Service"]
        DASH["Streamlit Dashboard"]
        AGW["API Gateway"]
  end
 subgraph subGraph2["Azure Services"]
        MAPS["Azure Maps"]
        EG["Event Grid"]
        BLOB["Blob Storage"]
        KV["Key Vault"]
        SQL["Azure SQL"]
  end
 subgraph subGraph3["Data Layer"]
        REDIS[("Redis Cache")]
        POSTGRES[("PostgreSQL")]
        MQTT["MQTT Broker"]
  end
 subgraph Monitoring["Monitoring"]
        PROM["Prometheus"]
        GRAF["Grafana"]
        LOGS["Log Analytics"]
  end
 subgraph subGraph5["Azure Cloud Infrastructure"]
        subGraph1
        subGraph2
        subGraph3
        Monitoring
  end
 subgraph subGraph6["External Integrations"]
        EMAIL["Email System"]
        SLACK["Slack"]
        TEAMS["Microsoft Teams"]
        SMS["SMS Gateway"]
        WEBHOOK["Custom Webhooks"]
  end
 subgraph subGraph7["User Access"]
        FM["Fleet Manager"]
        SI["Safety Inspector"]
        OM["Operations Manager"]
        EX["Executive"]
  end
    T1 --> API
    T2 --> API
    T100 --> API
    GPS --> API
    API --> F
    F --> AI & MQTT & BLOB & PROM
    AI --> GF & AL & POSTGRES & REDIS & PROM
    GF --> MAPS & EG & PROM
    AL --> EMAIL & SLACK & TEAMS & SMS & WEBHOOK & PROM
    DASH --> POSTGRES & REDIS & F
    AGW --> AI & GF & AL
    FM --> DASH
    SI --> DASH
    OM --> DASH
    EX --> DASH
    PROM --> GRAF
     T1:::truck
     T2:::truck
     T100:::truck
     GPS:::truck
     F:::service
     AI:::service
     GF:::service
     AL:::service
     DASH:::service
     AGW:::service
     MAPS:::azure
     EG:::azure
     BLOB:::azure
     KV:::azure
     SQL:::azure
     REDIS:::data
     POSTGRES:::data
     MQTT:::data
     EMAIL:::external
     SLACK:::external
     TEAMS:::external
     SMS:::external
     WEBHOOK:::external
     FM:::user
     SI:::user
     OM:::user
     EX:::user
    classDef truck fill:#e1f5fe
    classDef azure fill:#0078d4,color:#fff
    classDef service fill:#4caf50,color:#fff
    classDef data fill:#ff9800,color:#fff
    classDef external fill:#9c27b0,color:#fff
    classDef user fill:#795548,color:#fff
```

## Detailed Data Flow Diagram

```mermaid
sequenceDiagram
    participant C as CCTV Cameras
    participant API as CCTV API
    participant F as Frigate Engine
    participant AI as Agentic AI
    participant GF as Geo-fencing
    participant AL as Alerting
    participant DB as Database
    participant DASH as Dashboard
    participant USER as End Users

    %% Real-time Video Processing
    C->>API: Video Stream (RTSP/HTTP)
    API->>F: Processed Stream
    F->>F: Object Detection (YOLO)
    F->>DB: Store Detection Events
    F->>AI: Send Detection Data

    %% AI Analysis
    AI->>AI: Analyze Driver Behavior
    AI->>GF: Request Location Check
    GF->>GF: Check Geo-boundaries
    GF-->>AI: Location Compliance Status
    AI->>AI: Apply Safety Rules
    
    %% Violation Detection
    alt Violation Detected
        AI->>AL: Trigger Alert
        AL->>AL: Process Alert Rules
        AL->>DB: Store Violation
        
        par Multi-channel Alerts
            AL->>USER: Email Alert
        and
            AL->>USER: SMS Alert
        and
            AL->>USER: Slack Notification
        and
            AL->>USER: Teams Message
        end
    end

    %% Dashboard Updates
    loop Real-time Updates
        DASH->>DB: Query Latest Data
        DB-->>DASH: Return Data
        DASH->>USER: Update Dashboard
    end

    %% Escalation Process
    alt Alert Not Acknowledged
        AL->>AL: Wait Escalation Timer
        AL->>AL: Escalate Alert Level
        AL->>USER: Send Escalated Alert
    end
```

## Component Interaction Flow

```mermaid
---
config:
  layout: elk
---
flowchart LR
 subgraph subGraph0["Data Ingestion"]
        B["Stream Processing"]
        A["400 Camera Streams"]
        C["Frame Extraction"]
        D["Object Detection"]
  end
 subgraph subGraph1["AI Processing"]
        E["Agentic AI Analysis"]
        F["Rule Validation"]
        G["Decision Making"]
  end
 subgraph subGraph2["Location Services"]
        I["Geo-fencing Check"]
        H["GPS Data"]
        J["Route Compliance"]
  end
 subgraph subGraph3["Alert Management"]
        K{"Violation?"}
        L["Generate Alert"]
        M["Log Normal Event"]
        N["Multi-channel Dispatch"]
        O["Email/SMS/Slack/Teams"]
  end
 subgraph subGraph4["Data Storage"]
        P[("Event Database")]
        Q[("Video Clips")]
        R[("Analytics Data")]
  end
 subgraph subGraph5["User Interface"]
        S["Real-time Dashboard"]
        T["Fleet Managers"]
        U["Safety Inspectors"]
        V["Operations Team"]
        W["Executives"]
  end
    A --> B
    B --> C
    C --> D
    D --> E & Q
    E --> F & R
    F --> G
    H --> I
    I --> J
    J --> E
    G --> K
    K -- Yes --> L
    K -- No --> M
    L --> N & P
    N --> O
    M --> P
    P --> S
    Q --> S
    R --> S
    S --> T & U & V & W
     A:::input
     B:::process
     C:::process
     D:::process
     E:::process
     F:::process
     G:::process
     H:::input
     I:::process
     J:::process
     K:::decision
     O:::output
     P:::storage
     Q:::storage
     R:::storage
     T:::output
     U:::output
     V:::output
     W:::output
    classDef input fill:#e3f2fd
    classDef process fill:#e8f5e8
    classDef decision fill:#fff3e0
    classDef output fill:#fce4ec
    classDef storage fill:#f3e5f5

```

## Network Architecture Diagram

```mermaid
---
config:
  layout: elk
---
flowchart TB
 subgraph Internet["Internet"]
        INET["Public Internet"]
  end
 subgraph subGraph1["Public Subnet (10.0.1.0/24)"]
        LB["Load Balancer"]
        AG["Application Gateway"]
  end
 subgraph subGraph2["AKS Cluster"]
        POD1["Frigate Pods"]
        POD2["AI Service Pods"]
        POD3["Dashboard Pods"]
        POD4["API Gateway Pods"]
  end
 subgraph subGraph3["Private Subnet (10.0.2.0/24)"]
        subGraph2
  end
 subgraph subGraph4["Data Subnet (10.0.3.0/24)"]
        DB["Azure SQL"]
        CACHE["Redis Cache"]
        STORAGE["Blob Storage"]
  end
 subgraph subGraph5["Virtual Network (10.0.0.0/16)"]
        subGraph1
        subGraph3
        subGraph4
  end
 subgraph subGraph6["Azure Services"]
        MAPS["Azure Maps"]
        EVENTGRID["Event Grid"]
        KEYVAULT["Key Vault"]
        MONITOR["Monitor"]
  end
 subgraph subGraph7["Azure Cloud"]
        subGraph5
        subGraph6
  end
 subgraph s1["On-Premises/Edge"]
        TRUCK1["Truck Fleet"]
        OFFICE["Office Network"]
  end
    INET --> LB
    LB --> AG
    AG --> POD1 & POD2 & POD3 & POD4
    TRUCK1 -. HTTPS/VPN .-> AG
    OFFICE -. VPN .-> AG
    POD1 --> DB & CACHE & STORAGE
    POD2 --> MAPS & EVENTGRID
    POD1 -. NSG Rules .-> POD2
    POD2 -. NSG Rules .-> POD3
    POD3 -. Private Endpoint .-> DB
     LB:::public
     AG:::public
     POD1:::private
     POD2:::private
     POD3:::private
     POD4:::private
     DB:::data
     CACHE:::data
     STORAGE:::data
     MAPS:::service
     EVENTGRID:::service
     KEYVAULT:::service
     MONITOR:::service
    classDef public fill:#ffcdd2
    classDef private fill:#c8e6c9
    classDef data fill:#fff9c4
    classDef service fill:#e1bee7

```

## Data Processing Pipeline

```mermaid
---
config:
  layout: elk
---
flowchart TD
 subgraph subGraph0["Input Sources"]
        V1["Video Stream 1-100"]
        V2["Video Stream 101-200"]
        V3["Video Stream 201-300"]
        V4["Video Stream 301-400"]
        GPS["GPS Data Feed"]
        ROUTE["Route Data"]
  end
 subgraph subGraph1["Real-time Processing"]
        BUFFER["Stream Buffer"]
        DECODE["Video Decoder"]
        RESIZE["Frame Resizer"]
        DETECT["Object Detection"]
  end
 subgraph subGraph2["AI Analysis Engine"]
        EXTRACT["Feature Extraction"]
        CLASSIFY["Behavior Classification"]
        VALIDATE["Rule Validation"]
        SCORE["Risk Scoring"]
  end
 subgraph subGraph3["Decision Engine"]
        RULES{"Safety Rules"}
        GEO{"Geo-fencing"}
        SPEED{"Speed Check"}
        ALERT{"Alert Required?"}
  end
 subgraph subGraph4["Output Actions"]
        STORE["Store Event"]
        NOTIFY["Send Notification"]
        UPDATE["Update Dashboard"]
        LOG["Audit Log"]
  end
    V1 --> BUFFER
    V2 --> BUFFER
    V3 --> BUFFER
    V4 --> BUFFER
    BUFFER --> DECODE
    DECODE --> RESIZE
    RESIZE --> DETECT
    DETECT --> EXTRACT
    GPS --> EXTRACT
    ROUTE --> EXTRACT
    EXTRACT --> CLASSIFY
    CLASSIFY --> VALIDATE
    VALIDATE --> SCORE
    SCORE --> RULES & GEO & SPEED
    RULES --> ALERT
    GEO --> ALERT
    SPEED --> ALERT
    ALERT -- Yes --> NOTIFY & STORE
    ALERT -- Always --> UPDATE & LOG
    BUFFER -. Latency: &lt;1s .-> DECODE
    DETECT -. Throughput: 400fps .-> EXTRACT
    ALERT -. Response: &lt;5s .-> NOTIFY
     V1:::input
     V2:::input
     V3:::input
     V4:::input
     GPS:::input
     ROUTE:::input
     BUFFER:::process
     DECODE:::process
     RESIZE:::process
     DETECT:::process
     EXTRACT:::process
     CLASSIFY:::process
     VALIDATE:::process
     SCORE:::process
     RULES:::decision
     GEO:::decision
     SPEED:::decision
     ALERT:::decision
     STORE:::output
     NOTIFY:::output
     UPDATE:::output
     LOG:::output
    classDef input fill:#e3f2fd
    classDef process fill:#e8f5e8
    classDef decision fill:#fff3e0
    classDef output fill:#fce4ec

```

## Deployment Architecture

```mermaid
---
config:
  layout: elk
---
flowchart TB
 subgraph subGraph0["Development Environment"]
        DEV["Developer Workstation"]
        GIT["Git Repository"]
        CI["CI/CD Pipeline"]
  end
 subgraph subGraph1["Azure Container Registry"]
        ACR["Container Images"]
  end
 subgraph subGraph2["Staging Environment"]
        AKS_STAGE["AKS Staging Cluster"]
        TEST["Automated Testing"]
  end
 subgraph subGraph3["Primary Region (East US)"]
        AKS_PROD["AKS Production Cluster"]
        DB_PROD["Production Database"]
        STORAGE_PROD["Production Storage"]
  end
 subgraph subGraph4["Secondary Region (West US)"]
        AKS_DR["AKS DR Cluster"]
        DB_DR["DR Database"]
        STORAGE_DR["DR Storage"]
  end
 subgraph subGraph5["Production Environment"]
        subGraph3
        subGraph4
  end
 subgraph subGraph6["Monitoring & Management"]
        MONITOR["Azure Monitor"]
        BACKUP["Backup Services"]
        SECURITY["Security Center"]
  end
    DEV --> GIT
    GIT --> CI
    CI --> ACR
    ACR --> AKS_STAGE
    AKS_STAGE --> TEST
    TEST -- Pass --> AKS_PROD
    DB_PROD -. "Geo-replication" .-> DB_DR
    STORAGE_PROD -. Backup .-> STORAGE_DR
    AKS_PROD --> MONITOR & SECURITY
    AKS_DR --> MONITOR
    DB_PROD --> BACKUP & SECURITY
     DEV:::dev
     GIT:::dev
     CI:::dev
     AKS_STAGE:::staging
     TEST:::staging
     AKS_PROD:::prod
     DB_PROD:::prod
     STORAGE_PROD:::prod
     AKS_DR:::dr
     DB_DR:::dr
     STORAGE_DR:::dr
     MONITOR:::mgmt
     BACKUP:::mgmt
     SECURITY:::mgmt
    classDef dev fill:#e3f2fd
    classDef staging fill:#fff3e0
    classDef prod fill:#e8f5e8
    classDef dr fill:#fce4ec
    classDef mgmt fill:#f3e5f5

```

## Security Architecture

```mermaid
---
config:
  layout: elk
---
flowchart TB
 subgraph subGraph0["Identity & Access"]
        AAD["Azure Active Directory"]
        RBAC["Role-Based Access Control"]
        MFA["Multi-Factor Authentication"]
  end
 subgraph subGraph1["Network Security"]
        WAF["Web Application Firewall"]
        NSG["Network Security Groups"]
        VNET["Virtual Network"]
        PRIVATE["Private Endpoints"]
  end
 subgraph subGraph2["Application Security"]
        SSL["SSL/TLS Encryption"]
        SECRETS["Key Vault Secrets"]
        SCAN["Security Scanning"]
        POLICY["Security Policies"]
  end
 subgraph subGraph3["Data Security"]
        ENCRYPT["Encryption at Rest"]
        BACKUP["Encrypted Backups"]
        GDPR["GDPR Compliance"]
        AUDIT["Audit Logging"]
  end
 subgraph subGraph4["Monitoring & Response"]
        SIEM["Security Information & Event Management"]
        THREAT["Threat Detection"]
        INCIDENT["Incident Response"]
        COMPLIANCE["Compliance Reporting"]
  end
    AAD --> RBAC
    RBAC --> MFA
    WAF --> NSG
    NSG --> VNET
    VNET --> PRIVATE
    SSL --> SECRETS
    SECRETS --> SCAN
    SCAN --> POLICY
    ENCRYPT --> BACKUP
    BACKUP --> GDPR
    GDPR --> AUDIT
    SIEM --> THREAT
    THREAT --> INCIDENT
    INCIDENT --> COMPLIANCE
    AAD -.-> SSL
    SECRETS -.-> ENCRYPT
    AUDIT -.-> SIEM
    POLICY -.-> COMPLIANCE
     AAD:::identity
     RBAC:::identity
     MFA:::identity
     WAF:::network
     NSG:::network
     VNET:::network
     PRIVATE:::network
     SSL:::app
     SECRETS:::app
     SCAN:::app
     POLICY:::app
     ENCRYPT:::data
     BACKUP:::data
     GDPR:::data
     AUDIT:::data
     SIEM:::monitor
     THREAT:::monitor
     INCIDENT:::monitor
     COMPLIANCE:::monitor
    classDef identity fill:#e3f2fd
    classDef network fill:#e8f5e8
    classDef app fill:#fff3e0
    classDef data fill:#fce4ec
    classDef monitor fill:#f3e5f5

```

## Cost Optimization Flow

```mermaid
---
config:
  layout: elk
---
flowchart TB
 subgraph subGraph0["Resource Monitoring"]
        USAGE["Resource Usage Tracking"]
        METRICS["Cost Metrics Collection"]
        BASELINE["Baseline Establishment"]
  end
 subgraph subGraph1["Analysis & Optimization"]
        ANALYZE["Cost Analysis Engine"]
        IDENTIFY["Identify Inefficiencies"]
        RECOMMEND["Optimization Recommendations"]
  end
 subgraph subGraph2["Auto-scaling Decisions"]
        SCALE_UP{"Scale Up Needed?"}
        SCALE_DOWN{"Scale Down Possible?"}
        MAINTAIN["Maintain Current"]
  end
 subgraph subGraph3["Resource Actions"]
        ADD_NODES["Add AKS Nodes"]
        REMOVE_NODES["Remove AKS Nodes"]
        RESIZE_DB["Resize Database"]
        OPTIMIZE_STORAGE["Optimize Storage"]
  end
 subgraph subGraph4["Cost Controls"]
        BUDGET["Budget Alerts"]
        LIMITS["Resource Limits"]
        POLICIES["Cost Policies"]
        GOVERNANCE["Cost Governance"]
  end
 subgraph subGraph5["Reporting & Insights"]
        DASHBOARD["Cost Dashboard"]
        REPORTS["Monthly Reports"]
        FORECAST["Cost Forecasting"]
        ROI["ROI Analysis"]
  end
    USAGE --> METRICS
    METRICS --> BASELINE
    BASELINE --> ANALYZE
    ANALYZE --> IDENTIFY
    IDENTIFY --> RECOMMEND
    RECOMMEND --> SCALE_UP & SCALE_DOWN
    SCALE_UP -- Yes --> ADD_NODES & RESIZE_DB
    SCALE_DOWN -- Yes --> REMOVE_NODES & OPTIMIZE_STORAGE
    SCALE_UP -- No --> MAINTAIN
    SCALE_DOWN -- No --> MAINTAIN
    ADD_NODES --> BUDGET
    REMOVE_NODES --> BUDGET
    RESIZE_DB --> LIMITS
    OPTIMIZE_STORAGE --> POLICIES
    BUDGET --> GOVERNANCE
    LIMITS --> GOVERNANCE
    POLICIES --> GOVERNANCE
    GOVERNANCE --> DASHBOARD
    DASHBOARD --> REPORTS
    REPORTS --> FORECAST
    FORECAST --> ROI
    METRICS -.-> DASHBOARD
    ANALYZE -.-> REPORTS
     USAGE:::monitor
     METRICS:::monitor
     BASELINE:::monitor
     ANALYZE:::process
     IDENTIFY:::process
     RECOMMEND:::process
     SCALE_UP:::decision
     SCALE_DOWN:::decision
     MAINTAIN:::action
     ADD_NODES:::action
     REMOVE_NODES:::action
     RESIZE_DB:::action
     OPTIMIZE_STORAGE:::action
     BUDGET:::control
     LIMITS:::control
     POLICIES:::control
     GOVERNANCE:::control
     DASHBOARD:::output
     REPORTS:::output
     FORECAST:::output
     ROI:::output
    classDef monitor fill:#e3f2fd
    classDef process fill:#e8f5e8
    classDef decision fill:#fff3e0
    classDef action fill:#fce4ec
    classDef control fill:#f3e5f5
    classDef output fill:#e1f5fe
```

---

These diagrams provide a comprehensive visual representation of the system architecture, data flows, and technical implementation for your director's review. They clearly show the enterprise-grade nature of the solution and the thorough approach to security, scalability, and cost optimization.