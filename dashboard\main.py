"""
Professional Streamlit Dashboard for Asphalt Truck CCTV Monitoring System
Enterprise-grade dashboard for 100 trucks with 400 cameras

Features:
- Real-time fleet monitoring
- Interactive violation dashboard
- Route compliance tracking
- Driver behavior analytics
- Executive reporting
- Mobile-responsive design
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
import json
from datetime import datetime, timedelta
import time
import os
from typing import Dict, List, Optional
import logging
from dataclasses import dataclass
import asyncio
import folium
from streamlit_folium import st_folium
import cv2
from PIL import Image
import base64
import io

# Configure page
st.set_page_config(
    page_title="Asphalt Truck Fleet Monitor",
    page_icon="🚛",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TruckData:
    truck_id: int
    driver_name: str
    status: str
    speed: float
    latitude: float
    longitude: float
    route_id: str
    last_seen: datetime
    violations_today: int
    camera_status: List[bool]

@dataclass
class ViolationData:
    truck_id: int
    violation_type: str
    severity: str
    timestamp: datetime
    location: str
    description: str
    resolved: bool

class AsphaltTruckDashboard:
    """Main dashboard class for the Asphalt Truck monitoring system"""
    
    def __init__(self):
        self.init_session_state()
        self.setup_styling()
        self.api_base_url = os.getenv("API_BASE_URL", "http://localhost:8000")
        
    def init_session_state(self):
        """Initialize Streamlit session state"""
        if 'authenticated' not in st.session_state:
            st.session_state.authenticated = False
        if 'user_role' not in st.session_state:
            st.session_state.user_role = None
        if 'selected_truck' not in st.session_state:
            st.session_state.selected_truck = None
        if 'auto_refresh' not in st.session_state:
            st.session_state.auto_refresh = True
        if 'refresh_interval' not in st.session_state:
            st.session_state.refresh_interval = 30
    
    def setup_styling(self):
        """Setup custom CSS styling"""
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .metric-card {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #1f77b4;
        }
        
        .violation-critical {
            border-left-color: #d62728 !important;
        }
        
        .violation-high {
            border-left-color: #ff7f0e !important;
        }
        
        .violation-medium {
            border-left-color: #ffbb78 !important;
        }
        
        .violation-low {
            border-left-color: #2ca02c !important;
        }
        
        .status-active {
            color: #2ca02c;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #d62728;
            font-weight: bold;
        }
        
        .status-warning {
            color: #ff7f0e;
            font-weight: bold;
        }
        
        .sidebar-info {
            background: #f0f2f6;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .truck-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .camera-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 1rem;
        }
        
        .camera-feed {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 5px;
        }
        
        .camera-feed.active {
            border-color: #2ca02c;
        }
        
        .camera-feed.violation {
            border-color: #d62728;
            animation: blink 1s linear infinite;
        }
        
        @keyframes blink {
            0%, 50% { border-color: #d62728; }
            51%, 100% { border-color: transparent; }
        }
        
        .real-time-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #2ca02c;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }
        </style>
        """, unsafe_allow_html=True)
    
    def authenticate_user(self):
        """Handle user authentication"""
        if not st.session_state.authenticated:
            st.markdown('<div class="main-header">🚛 Asphalt Truck Fleet Monitor</div>', unsafe_allow_html=True)
            
            with st.container():
                col1, col2, col3 = st.columns([1, 2, 1])
                with col2:
                    st.markdown("### 🔐 Login Required")
                    
                    username = st.text_input("Username", placeholder="Enter your username")
                    password = st.text_input("Password", type="password", placeholder="Enter your password")
                    role = st.selectbox("Role", ["Fleet Manager", "Operations Manager", "Safety Inspector", "Executive"])
                    
                    if st.button("Login", type="primary", use_container_width=True):
                        # Simple authentication (replace with real auth)
                        if username and password:
                            st.session_state.authenticated = True
                            st.session_state.user_role = role
                            st.session_state.username = username
                            st.rerun()
                        else:
                            st.error("Please enter valid credentials")
            
            # Demo credentials info
            with st.expander("🔍 Demo Credentials"):
                st.markdown("""
                **Demo Login:**
                - Username: `demo`
                - Password: `demo123`
                - Role: Any role from dropdown
                
                **Available Roles:**
                - **Fleet Manager**: Full access to all trucks and operations
                - **Operations Manager**: Route and scheduling management
                - **Safety Inspector**: Violation monitoring and compliance
                - **Executive**: High-level dashboards and reports
                """)
            
            return False
        return True
    
    def render_sidebar(self):
        """Render the sidebar with navigation and filters"""
        with st.sidebar:
            # User info
            st.markdown(f"""
            <div class="sidebar-info">
                <strong>👤 {st.session_state.username}</strong><br>
                <small>{st.session_state.user_role}</small>
            </div>
            """, unsafe_allow_html=True)
            
            # Logout button
            if st.button("🚪 Logout"):
                st.session_state.authenticated = False
                st.session_state.user_role = None
                st.rerun()
            
            st.markdown("---")
            
            # Real-time status
            st.markdown("""
            <div style="text-align: center;">
                <span class="real-time-indicator"></span>
                <strong> LIVE MONITORING</strong>
            </div>
            """, unsafe_allow_html=True)
            
            # Navigation
            st.markdown("### 📊 Dashboard Sections")
            page = st.radio(
                "Select View",
                ["🏠 Fleet Overview", "🚛 Truck Details", "⚠️ Violations", "📍 Route Monitor", "📈 Analytics", "⚙️ Settings"],
                key="navigation"
            )
            
            st.markdown("---")
            
            # Quick filters
            st.markdown("### 🔍 Quick Filters")
            
            # Truck status filter
            status_filter = st.multiselect(
                "Truck Status",
                ["Active", "Inactive", "Maintenance", "Violation"],
                default=["Active"]
            )
            
            # Violation severity filter
            if "Violations" in page:
                severity_filter = st.multiselect(
                    "Violation Severity",
                    ["Critical", "High", "Medium", "Low"],
                    default=["Critical", "High"]
                )
            
            # Time range filter
            time_range = st.selectbox(
                "Time Range",
                ["Last Hour", "Last 4 Hours", "Today", "Last 7 Days", "Last 30 Days"],
                index=2
            )
            
            st.markdown("---")
            
            # System status
            st.markdown("### 📡 System Status")
            
            # Mock system status
            col1, col2 = st.columns(2)
            with col1:
                st.metric("API Status", "🟢 Online")
                st.metric("Cameras", "396/400")
            with col2:
                st.metric("AI Agent", "🟢 Active")
                st.metric("Alerts", "5 New")
            
            # Auto-refresh controls
            st.markdown("---")
            st.markdown("### ⚙️ Refresh Settings")
            
            auto_refresh = st.checkbox("Auto Refresh", value=st.session_state.auto_refresh)
            if auto_refresh:
                refresh_interval = st.slider("Refresh Interval (seconds)", 10, 300, st.session_state.refresh_interval)
                st.session_state.refresh_interval = refresh_interval
            
            st.session_state.auto_refresh = auto_refresh
            
            return page, status_filter, time_range
    
    def get_sample_data(self) -> tuple:
        """Generate sample data for demonstration"""
        # Sample truck data
        trucks = []
        for i in range(1, 101):  # 100 trucks
            truck = TruckData(
                truck_id=i,
                driver_name=f"Driver {i:03d}",
                status=np.random.choice(["Active", "Inactive", "Maintenance", "Violation"], p=[0.7, 0.1, 0.1, 0.1]),
                speed=np.random.uniform(30, 80),
                latitude=47.6062 + np.random.uniform(-0.1, 0.1),
                longitude=-122.3321 + np.random.uniform(-0.1, 0.1),
                route_id=f"Route-{np.random.randint(1, 21):02d}",
                last_seen=datetime.now() - timedelta(minutes=np.random.randint(0, 60)),
                violations_today=np.random.randint(0, 5),
                camera_status=[np.random.choice([True, False], p=[0.9, 0.1]) for _ in range(4)]
            )
            trucks.append(truck)
        
        # Sample violation data
        violations = []
        violation_types = ["Phone Use", "No Seatbelt", "Fatigue", "Speed Violation", "Route Deviation", "Unauthorized Stop"]
        severities = ["Critical", "High", "Medium", "Low"]
        
        for i in range(50):  # 50 recent violations
            violation = ViolationData(
                truck_id=np.random.randint(1, 101),
                violation_type=np.random.choice(violation_types),
                severity=np.random.choice(severities),
                timestamp=datetime.now() - timedelta(hours=np.random.randint(0, 24)),
                location=f"Route {np.random.randint(1, 21)}, Mile {np.random.randint(1, 100)}",
                description=f"Automated detection: {np.random.choice(violation_types)}",
                resolved=np.random.choice([True, False], p=[0.7, 0.3])
            )
            violations.append(violation)
        
        return trucks, violations
    
    def render_fleet_overview(self, trucks: List[TruckData]):
        """Render the fleet overview page"""
        st.markdown('<div class="main-header">🚛 Fleet Overview</div>', unsafe_allow_html=True)
        
        # Key metrics
        col1, col2, col3, col4, col5 = st.columns(5)
        
        active_trucks = len([t for t in trucks if t.status == "Active"])
        total_violations = sum(t.violations_today for t in trucks)
        avg_speed = np.mean([t.speed for t in trucks if t.status == "Active"])
        cameras_online = sum(sum(t.camera_status) for t in trucks)
        
        with col1:
            st.metric("Active Trucks", f"{active_trucks}/100", f"{active_trucks - 95} from target")
        with col2:
            st.metric("Total Violations", total_violations, f"+{np.random.randint(1, 5)} today")
        with col3:
            st.metric("Avg Speed", f"{avg_speed:.1f} km/h", f"{avg_speed - 55:.1f} km/h")
        with col4:
            st.metric("Cameras Online", f"{cameras_online}/400", f"{cameras_online - 396} from yesterday")
        with col5:
            st.metric("System Health", "98.5%", "+0.2%")
        
        # Charts row
        col1, col2 = st.columns(2)
        
        with col1:
            # Truck status distribution
            status_counts = pd.Series([t.status for t in trucks]).value_counts()
            fig_status = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="Truck Status Distribution",
                color_discrete_map={
                    "Active": "#2ca02c",
                    "Inactive": "#d62728",
                    "Maintenance": "#ff7f0e",
                    "Violation": "#9467bd"
                }
            )
            fig_status.update_layout(height=400)
            st.plotly_chart(fig_status, use_container_width=True)
        
        with col2:
            # Speed distribution
            speeds = [t.speed for t in trucks if t.status == "Active"]
            fig_speed = px.histogram(
                x=speeds,
                nbins=20,
                title="Speed Distribution (Active Trucks)",
                labels={'x': 'Speed (km/h)', 'y': 'Count'}
            )
            fig_speed.add_vline(x=60, line_dash="dash", line_color="red", annotation_text="Speed Limit")
            fig_speed.update_layout(height=400)
            st.plotly_chart(fig_speed, use_container_width=True)
        
        # Real-time map
        st.markdown("### 🗺️ Real-Time Fleet Location")
        
        # Create folium map
        center_lat = 47.6062
        center_lon = -122.3321
        
        m = folium.Map(location=[center_lat, center_lon], zoom_start=11)
        
        # Add truck markers
        for truck in trucks:
            if truck.status == "Active":
                color = "green"
                icon = "truck"
            elif truck.status == "Violation":
                color = "red"
                icon = "exclamation-triangle"
            elif truck.status == "Maintenance":
                color = "orange"
                icon = "wrench"
            else:
                color = "gray"
                icon = "pause"
            
            popup_text = f"""
            <b>Truck {truck.truck_id}</b><br>
            Driver: {truck.driver_name}<br>
            Status: {truck.status}<br>
            Speed: {truck.speed:.1f} km/h<br>
            Route: {truck.route_id}<br>
            Violations Today: {truck.violations_today}
            """
            
            folium.Marker(
                [truck.latitude, truck.longitude],
                popup=folium.Popup(popup_text, max_width=300),
                tooltip=f"Truck {truck.truck_id}",
                icon=folium.Icon(color=color, icon=icon, prefix='fa')
            ).add_to(m)
        
        # Display map
        map_data = st_folium(m, width=None, height=500)
        
        # Truck list table
        st.markdown("### 📋 Fleet Status Table")
        
        # Convert to DataFrame for better display
        truck_df = pd.DataFrame([
            {
                "Truck ID": t.truck_id,
                "Driver": t.driver_name,
                "Status": t.status,
                "Speed (km/h)": f"{t.speed:.1f}",
                "Route": t.route_id,
                "Violations": t.violations_today,
                "Cameras": f"{sum(t.camera_status)}/4",
                "Last Seen": t.last_seen.strftime("%H:%M")
            }
            for t in trucks
        ])
        
        # Apply styling based on status
        def style_status(val):
            if val == "Active":
                return "background-color: #d4edda; color: #155724"
            elif val == "Violation":
                return "background-color: #f8d7da; color: #721c24"
            elif val == "Maintenance":
                return "background-color: #fff3cd; color: #856404"
            else:
                return "background-color: #e2e3e5; color: #383d41"
        
        styled_df = truck_df.style.applymap(style_status, subset=['Status'])
        st.dataframe(styled_df, use_container_width=True, height=400)
    
    def render_truck_details(self, trucks: List[TruckData]):
        """Render detailed view for a specific truck"""
        st.markdown('<div class="main-header">🚛 Truck Details</div>', unsafe_allow_html=True)
        
        # Truck selection
        truck_ids = [t.truck_id for t in trucks]
        selected_id = st.selectbox("Select Truck", truck_ids, index=0 if not st.session_state.selected_truck else truck_ids.index(st.session_state.selected_truck) if st.session_state.selected_truck in truck_ids else 0)
        st.session_state.selected_truck = selected_id
        
        # Find selected truck
        selected_truck = next(t for t in trucks if t.truck_id == selected_id)
        
        # Truck info header
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.markdown(f"""
            <div class="truck-card">
                <h3>🚛 Truck {selected_truck.truck_id}</h3>
                <p><strong>Driver:</strong> {selected_truck.driver_name}</p>
                <p><strong>Status:</strong> <span class="status-{selected_truck.status.lower()}">{selected_truck.status}</span></p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div class="truck-card">
                <h4>📊 Current Metrics</h4>
                <p><strong>Speed:</strong> {selected_truck.speed:.1f} km/h</p>
                <p><strong>Route:</strong> {selected_truck.route_id}</p>
                <p><strong>Last Seen:</strong> {selected_truck.last_seen.strftime('%H:%M:%S')}</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            st.markdown(f"""
            <div class="truck-card">
                <h4>⚠️ Violations</h4>
                <p><strong>Today:</strong> {selected_truck.violations_today}</p>
                <p><strong>This Week:</strong> {selected_truck.violations_today * 7}</p>
                <p><strong>This Month:</strong> {selected_truck.violations_today * 30}</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            cameras_online = sum(selected_truck.camera_status)
            st.markdown(f"""
            <div class="truck-card">
                <h4>📹 Cameras</h4>
                <p><strong>Online:</strong> {cameras_online}/4</p>
                <p><strong>Recording:</strong> {cameras_online}/4</p>
                <p><strong>AI Active:</strong> {cameras_online}/4</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Camera feeds
        st.markdown("### 📹 Live Camera Feeds")
        
        # Create 2x2 grid for camera feeds
        col1, col2 = st.columns(2)
        
        camera_names = ["Driver Camera", "Front Road", "Rear Camera", "Side Mirror"]
        
        for i, (cam_name, is_online) in enumerate(zip(camera_names, selected_truck.camera_status)):
            col = col1 if i % 2 == 0 else col2
            
            with col:
                if is_online:
                    # Simulate camera feed with placeholder
                    st.markdown(f"**{cam_name}** 🟢 Online")
                    
                    # Generate placeholder image
                    placeholder_img = np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8)
                    
                    # Add some truck interior elements for driver camera
                    if i == 0:  # Driver camera
                        cv2.rectangle(placeholder_img, (50, 50), (150, 120), (255, 255, 255), -1)  # Windshield
                        cv2.circle(placeholder_img, (160, 160), 30, (100, 100, 100), -1)  # Steering wheel
                    
                    st.image(placeholder_img, caption=f"Truck {selected_id} - {cam_name}", use_column_width=True)
                    
                    # Detection info
                    if np.random.random() > 0.8:  # 20% chance of detection
                        detection_type = np.random.choice(["Person Detected", "Phone Detected", "No Seatbelt"])
                        confidence = np.random.uniform(0.7, 0.95)
                        st.markdown(f"🔍 **{detection_type}** (Confidence: {confidence:.1%})")
                else:
                    st.markdown(f"**{cam_name}** 🔴 Offline")
                    st.error("Camera connection lost")
        
        # Recent events timeline
        st.markdown("### 📝 Recent Events Timeline")
        
        # Sample events for the selected truck
        events = [
            {"time": "14:32", "event": "Route deviation detected", "severity": "Medium"},
            {"time": "14:15", "event": "Speed limit compliance", "severity": "Low"},
            {"time": "13:58", "event": "Phone use detected", "severity": "High"},
            {"time": "13:45", "event": "Seatbelt check passed", "severity": "Low"},
            {"time": "13:30", "event": "Route checkpoint reached", "severity": "Low"},
        ]
        
        for event in events:
            severity_color = {
                "Critical": "🔴",
                "High": "🟠",
                "Medium": "🟡",
                "Low": "🟢"
            }
            
            st.markdown(f"{severity_color[event['severity']]} **{event['time']}** - {event['event']}")
        
        # Performance charts
        st.markdown("### 📈 Performance Charts")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Speed over time
            times = pd.date_range(start=datetime.now() - timedelta(hours=8), periods=48, freq='10T')
            speeds = 45 + 15 * np.random.random(48) + 10 * np.sin(np.arange(48) * 0.3)
            
            fig_speed = px.line(
                x=times,
                y=speeds,
                title=f"Speed Profile - Truck {selected_id}",
                labels={'x': 'Time', 'y': 'Speed (km/h)'}
            )
            fig_speed.add_hline(y=60, line_dash="dash", line_color="red", annotation_text="Speed Limit")
            st.plotly_chart(fig_speed, use_container_width=True)
        
        with col2:
            # Violation count over days
            days = pd.date_range(start=datetime.now() - timedelta(days=7), periods=7, freq='D')
            violations = np.random.poisson(2, 7)
            
            fig_violations = px.bar(
                x=days,
                y=violations,
                title=f"Daily Violations - Truck {selected_id}",
                labels={'x': 'Date', 'y': 'Violations'}
            )
            st.plotly_chart(fig_violations, use_container_width=True)
    
    def render_violations_dashboard(self, violations: List[ViolationData]):
        """Render the violations monitoring dashboard"""
        st.markdown('<div class="main-header">⚠️ Violations Dashboard</div>', unsafe_allow_html=True)
        
        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)
        
        total_violations = len(violations)
        critical_violations = len([v for v in violations if v.severity == "Critical"])
        unresolved_violations = len([v for v in violations if not v.resolved])
        avg_resolution_time = np.random.uniform(2, 8)  # hours
        
        with col1:
            st.metric("Total Violations", total_violations, f"+{np.random.randint(1, 5)} today")
        with col2:
            st.metric("Critical", critical_violations, f"{critical_violations - 3} vs yesterday")
        with col3:
            st.metric("Unresolved", unresolved_violations, f"-{np.random.randint(1, 3)} resolved")
        with col4:
            st.metric("Avg Resolution", f"{avg_resolution_time:.1f}h", f"-{avg_resolution_time - 6:.1f}h")
        
        # Charts
        col1, col2 = st.columns(2)
        
        with col1:
            # Violations by type
            violation_counts = pd.Series([v.violation_type for v in violations]).value_counts()
            fig_types = px.bar(
                x=violation_counts.values,
                y=violation_counts.index,
                orientation='h',
                title="Violations by Type",
                labels={'x': 'Count', 'y': 'Violation Type'}
            )
            st.plotly_chart(fig_types, use_container_width=True)
        
        with col2:
            # Violations by severity
            severity_counts = pd.Series([v.severity for v in violations]).value_counts()
            colors = {'Critical': '#d62728', 'High': '#ff7f0e', 'Medium': '#ffbb78', 'Low': '#2ca02c'}
            fig_severity = px.pie(
                values=severity_counts.values,
                names=severity_counts.index,
                title="Violations by Severity",
                color=severity_counts.index,
                color_discrete_map=colors
            )
            st.plotly_chart(fig_severity, use_container_width=True)
        
        # Real-time violations feed
        st.markdown("### 🚨 Real-Time Violations Feed")
        
        # Filter controls
        col1, col2, col3 = st.columns(3)
        with col1:
            severity_filter = st.multiselect("Filter by Severity", ["Critical", "High", "Medium", "Low"], default=["Critical", "High"])
        with col2:
            status_filter = st.selectbox("Filter by Status", ["All", "Resolved", "Unresolved"])
        with col3:
            auto_refresh = st.checkbox("Auto Refresh Feed", value=True)
        
        # Violations table
        violations_df = pd.DataFrame([
            {
                "Time": v.timestamp.strftime("%H:%M:%S"),
                "Truck": f"Truck {v.truck_id}",
                "Type": v.violation_type,
                "Severity": v.severity,
                "Location": v.location,
                "Status": "✅ Resolved" if v.resolved else "🔄 Pending",
                "Description": v.description
            }
            for v in violations
            if v.severity in severity_filter and (status_filter == "All" or (status_filter == "Resolved") == v.resolved)
        ])
        
        # Apply color coding
        def highlight_severity(row):
            if row['Severity'] == 'Critical':
                return ['background-color: #ffebee'] * len(row)
            elif row['Severity'] == 'High':
                return ['background-color: #fff3e0'] * len(row)
            elif row['Severity'] == 'Medium':
                return ['background-color: #fffde7'] * len(row)
            else:
                return ['background-color: #e8f5e8'] * len(row)
        
        if not violations_df.empty:
            styled_violations = violations_df.style.apply(highlight_severity, axis=1)
            st.dataframe(styled_violations, use_container_width=True, height=400)
        else:
            st.info("No violations match the current filters.")
        
        # Violation details modal
        if not violations_df.empty:
            st.markdown("### 🔍 Violation Details")
            selected_violation = st.selectbox("Select violation for details", range(len(violations_df)), format_func=lambda x: f"{violations_df.iloc[x]['Time']} - {violations_df.iloc[x]['Truck']} - {violations_df.iloc[x]['Type']}")
            
            if selected_violation is not None:
                violation = violations[selected_violation]
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown(f"""
                    **Truck ID:** {violation.truck_id}  
                    **Type:** {violation.violation_type}  
                    **Severity:** {violation.severity}  
                    **Time:** {violation.timestamp.strftime('%Y-%m-%d %H:%M:%S')}  
                    **Location:** {violation.location}  
                    **Status:** {'Resolved' if violation.resolved else 'Unresolved'}
                    """)
                
                with col2:
                    # Show related camera footage placeholder
                    st.markdown("**Related Footage:**")
                    footage_img = np.random.randint(0, 255, (200, 300, 3), dtype=np.uint8)
                    st.image(footage_img, caption=f"Evidence - {violation.violation_type}")
                    
                    if not violation.resolved:
                        if st.button("Mark as Resolved", type="primary"):
                            st.success("Violation marked as resolved!")
                            st.rerun()
    
    def render_route_monitor(self, trucks: List[TruckData]):
        """Render route monitoring dashboard"""
        st.markdown('<div class="main-header">📍 Route Monitor</div>', unsafe_allow_html=True)
        
        # Route compliance metrics
        col1, col2, col3, col4 = st.columns(4)
        
        on_route = len([t for t in trucks if t.status == "Active"])
        total_routes = 20
        avg_deviation = np.random.uniform(10, 50)
        delays = np.random.randint(0, 5)
        
        with col1:
            st.metric("On Route", f"{on_route}/{len(trucks)}", f"+{on_route - 90}")
        with col2:
            st.metric("Active Routes", f"{total_routes}", "2 modified")
        with col3:
            st.metric("Avg Deviation", f"{avg_deviation:.0f}m", f"{avg_deviation - 30:.0f}m")
        with col4:
            st.metric("Delays", delays, f"-{delays - 2}")
        
        # Route map with geofencing
        st.markdown("### 🗺️ Route Map with Geo-fencing")
        
        # Create map with routes and geofences
        m = folium.Map(location=[47.6062, -122.3321], zoom_start=10)
        
        # Add sample routes
        route_coords = [
            [[47.6062, -122.3321], [47.6162, -122.3221], [47.6262, -122.3121]],
            [[47.6062, -122.3321], [47.5962, -122.3421], [47.5862, -122.3521]],
            [[47.6062, -122.3321], [47.6162, -122.3421], [47.6262, -122.3521]]
        ]
        
        for i, route in enumerate(route_coords):
            folium.PolyLine(
                route,
                color=f"#{['ff0000', '00ff00', '0000ff'][i]}",
                weight=5,
                opacity=0.8,
                popup=f"Route {i+1}"
            ).add_to(m)
        
        # Add geofence zones
        restricted_zones = [
            [47.6162, -122.3321, "Construction Zone"],
            [47.5962, -122.3221, "School Zone"],
            [47.6262, -122.3421, "Restricted Area"]
        ]
        
        for lat, lon, name in restricted_zones:
            folium.Circle(
                location=[lat, lon],
                radius=500,
                color='red',
                fill=True,
                fillColor='red',
                fillOpacity=0.3,
                popup=name
            ).add_to(m)
        
        # Add truck positions
        for truck in trucks[:10]:  # Show first 10 trucks
            if truck.status == "Active":
                folium.Marker(
                    [truck.latitude, truck.longitude],
                    popup=f"Truck {truck.truck_id}",
                    icon=folium.Icon(color='green', icon='truck', prefix='fa')
                ).add_to(m)
        
        st_folium(m, width=None, height=500)
        
        # Route compliance table
        st.markdown("### 📊 Route Compliance Details")
        
        route_data = []
        for truck in trucks[:20]:  # Show first 20 trucks
            deviation = np.random.uniform(0, 150)
            eta_variance = np.random.randint(-30, 60)
            
            route_data.append({
                "Truck": truck.truck_id,
                "Route": truck.route_id,
                "Progress": f"{np.random.randint(10, 90)}%",
                "Deviation": f"{deviation:.0f}m",
                "ETA Variance": f"{eta_variance:+d} min",
                "Status": "🟢 On Track" if deviation < 100 else "🟡 Minor Deviation" if deviation < 150 else "🔴 Major Deviation"
            })
        
        route_df = pd.DataFrame(route_data)
        st.dataframe(route_df, use_container_width=True)
    
    def render_analytics(self, trucks: List[TruckData], violations: List[ViolationData]):
        """Render analytics dashboard"""
        st.markdown('<div class="main-header">📈 Analytics Dashboard</div>', unsafe_allow_html=True)
        
        # Time period selector
        col1, col2, col3 = st.columns(3)
        with col1:
            time_period = st.selectbox("Time Period", ["Today", "This Week", "This Month", "Last 3 Months"])
        with col2:
            comparison = st.selectbox("Compare To", ["Previous Period", "Same Period Last Year", "Fleet Average"])
        with col3:
            export_format = st.selectbox("Export Format", ["PDF Report", "Excel", "CSV"])
        
        # Key performance indicators
        st.markdown("### 📊 Key Performance Indicators")
        
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            st.metric("Fleet Efficiency", "94.2%", "+2.1%")
        with col2:
            st.metric("Safety Score", "87.5%", "-1.2%")
        with col3:
            st.metric("Route Compliance", "96.8%", "+0.5%")
        with col4:
            st.metric("Driver Performance", "89.3%", "+1.8%")
        with col5:
            st.metric("Cost per Mile", "$2.34", "-$0.12")
        
        # Advanced charts
        tab1, tab2, tab3, tab4 = st.tabs(["Trends", "Heatmaps", "Correlations", "Predictions"])
        
        with tab1:
            # Trend analysis
            st.markdown("#### 📈 Trend Analysis")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # Violations trend
                dates = pd.date_range(start=datetime.now() - timedelta(days=30), periods=30, freq='D')
                violation_counts = np.random.poisson(8, 30)
                
                fig_trend = px.line(
                    x=dates,
                    y=violation_counts,
                    title="Daily Violations Trend",
                    labels={'x': 'Date', 'y': 'Violations'}
                )
                fig_trend.add_scatter(
                    x=dates,
                    y=np.poly1d(np.polyfit(range(30), violation_counts, 1))(range(30)),
                    mode='lines',
                    name='Trend Line',
                    line=dict(dash='dash')
                )
                st.plotly_chart(fig_trend, use_container_width=True)
            
            with col2:
                # Fleet utilization
                hours = list(range(24))
                utilization = [50 + 30 * np.sin((h - 6) * np.pi / 12) + np.random.normal(0, 5) for h in hours]
                utilization = [max(0, min(100, u)) for u in utilization]
                
                fig_util = px.line(
                    x=hours,
                    y=utilization,
                    title="Fleet Utilization by Hour",
                    labels={'x': 'Hour of Day', 'y': 'Utilization %'}
                )
                st.plotly_chart(fig_util, use_container_width=True)
        
        with tab2:
            # Heatmap analysis
            st.markdown("#### 🔥 Heatmap Analysis")
            
            # Violation heatmap by hour and day
            days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            hours = list(range(24))
            
            # Generate sample heatmap data
            heatmap_data = np.random.poisson(3, (7, 24))
            
            fig_heatmap = px.imshow(
                heatmap_data,
                x=hours,
                y=days,
                aspect="auto",
                color_continuous_scale="Reds",
                title="Violations Heatmap (Day vs Hour)",
                labels={'x': 'Hour of Day', 'y': 'Day of Week', 'color': 'Violations'}
            )
            st.plotly_chart(fig_heatmap, use_container_width=True)
        
        with tab3:
            # Correlation analysis
            st.markdown("#### 🔗 Correlation Analysis")
            
            # Generate correlation data
            factors = ['Speed', 'Route Deviation', 'Driver Experience', 'Weather Score', 'Traffic Density']
            correlation_matrix = np.random.rand(5, 5)
            correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
            np.fill_diagonal(correlation_matrix, 1)
            
            fig_corr = px.imshow(
                correlation_matrix,
                x=factors,
                y=factors,
                color_continuous_scale="RdBu",
                aspect="auto",
                title="Factor Correlation Matrix"
            )
            st.plotly_chart(fig_corr, use_container_width=True)
        
        with tab4:
            # Predictive analytics
            st.markdown("#### 🔮 Predictive Analytics")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # Predicted violations
                future_dates = pd.date_range(start=datetime.now(), periods=7, freq='D')
                predicted_violations = np.random.poisson(6, 7)
                confidence_upper = predicted_violations + np.random.randint(1, 4, 7)
                confidence_lower = predicted_violations - np.random.randint(1, 3, 7)
                confidence_lower = np.maximum(confidence_lower, 0)
                
                fig_pred = go.Figure()
                fig_pred.add_trace(go.Scatter(
                    x=future_dates,
                    y=predicted_violations,
                    mode='lines+markers',
                    name='Predicted',
                    line=dict(color='blue')
                ))
                fig_pred.add_trace(go.Scatter(
                    x=list(future_dates) + list(future_dates[::-1]),
                    y=list(confidence_upper) + list(confidence_lower[::-1]),
                    fill='toself',
                    fillcolor='rgba(0,100,80,0.2)',
                    line=dict(color='rgba(255,255,255,0)'),
                    name='Confidence Interval'
                ))
                fig_pred.update_layout(title="7-Day Violation Prediction")
                st.plotly_chart(fig_pred, use_container_width=True)
            
            with col2:
                # Risk assessment
                risk_categories = ['High Risk', 'Medium Risk', 'Low Risk']
                risk_trucks = [15, 35, 50]
                
                fig_risk = px.pie(
                    values=risk_trucks,
                    names=risk_categories,
                    title="Fleet Risk Assessment",
                    color_discrete_map={
                        'High Risk': '#d62728',
                        'Medium Risk': '#ff7f0e',
                        'Low Risk': '#2ca02c'
                    }
                )
                st.plotly_chart(fig_risk, use_container_width=True)
        
        # Export functionality
        if st.button("Generate Report", type="primary"):
            st.success(f"Report generated successfully! Download format: {export_format}")
            # In a real implementation, this would generate and download the actual report
    
    def render_settings(self):
        """Render settings page"""
        st.markdown('<div class="main-header">⚙️ Settings</div>', unsafe_allow_html=True)
        
        tab1, tab2, tab3, tab4 = st.tabs(["System", "Alerts", "Users", "Integration"])
        
        with tab1:
            st.markdown("#### System Configuration")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Monitoring Settings")
                refresh_rate = st.slider("Auto-refresh Rate (seconds)", 10, 300, 30)
                max_violations = st.number_input("Max Violations per Dashboard", min_value=10, max_value=1000, value=100)
                data_retention = st.selectbox("Data Retention Period", ["7 days", "30 days", "90 days", "1 year"])
                
                st.subheader("Camera Settings")
                camera_resolution = st.selectbox("Default Resolution", ["720p", "1080p", "4K"])
                recording_quality = st.slider("Recording Quality", 1, 10, 8)
                motion_sensitivity = st.slider("Motion Detection Sensitivity", 1, 10, 5)
            
            with col2:
                st.subheader("AI Model Settings")
                detection_confidence = st.slider("Detection Confidence Threshold", 0.1, 1.0, 0.7)
                model_version = st.selectbox("AI Model Version", ["v1.0", "v1.1", "v2.0 (Beta)"])
                auto_update = st.checkbox("Auto-update Models", value=True)
                
                st.subheader("Performance Settings")
                cpu_usage_limit = st.slider("Max CPU Usage (%)", 50, 100, 80)
                memory_limit = st.slider("Max Memory Usage (GB)", 8, 64, 32)
                concurrent_streams = st.number_input("Max Concurrent Streams", min_value=10, max_value=500, value=400)
        
        with tab2:
            st.markdown("#### Alert Configuration")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Alert Types")
                
                alert_types = [
                    "Phone Use Detection",
                    "Seatbelt Violation",
                    "Fatigue Detection",
                    "Speed Violation",
                    "Route Deviation",
                    "Unauthorized Stop",
                    "System Error"
                ]
                
                for alert_type in alert_types:
                    enabled = st.checkbox(f"Enable {alert_type}", value=True, key=f"alert_{alert_type}")
                    if enabled:
                        severity = st.selectbox(f"Severity", ["Low", "Medium", "High", "Critical"], key=f"severity_{alert_type}")
            
            with col2:
                st.subheader("Notification Settings")
                
                email_alerts = st.checkbox("Email Alerts", value=True)
                if email_alerts:
                    email_addresses = st.text_area("Email Addresses (one per line)", value="<EMAIL>\<EMAIL>")
                
                sms_alerts = st.checkbox("SMS Alerts", value=False)
                if sms_alerts:
                    phone_numbers = st.text_area("Phone Numbers (one per line)")
                
                webhook_alerts = st.checkbox("Webhook Alerts", value=True)
                if webhook_alerts:
                    webhook_url = st.text_input("Webhook URL", value="https://api.company.com/alerts")
                
                alert_frequency = st.selectbox("Alert Frequency", ["Immediate", "Every 5 minutes", "Every 15 minutes", "Hourly"])
        
        with tab3:
            st.markdown("#### User Management")
            
            # User list
            users = [
                {"name": "John Manager", "role": "Fleet Manager", "email": "<EMAIL>", "active": True},
                {"name": "Sarah Safety", "role": "Safety Inspector", "email": "<EMAIL>", "active": True},
                {"name": "Mike Ops", "role": "Operations Manager", "email": "<EMAIL>", "active": False},
            ]
            
            st.subheader("Current Users")
            user_df = pd.DataFrame(users)
            st.dataframe(user_df, use_container_width=True)
            
            # Add new user
            st.subheader("Add New User")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                new_name = st.text_input("Full Name")
                new_email = st.text_input("Email Address")
            
            with col2:
                new_role = st.selectbox("Role", ["Fleet Manager", "Operations Manager", "Safety Inspector", "Executive", "Viewer"])
                new_permissions = st.multiselect("Permissions", ["View Dashboard", "Manage Alerts", "Export Data", "User Management", "System Settings"])
            
            with col3:
                if st.button("Add User", type="primary"):
                    st.success(f"User {new_name} added successfully!")
        
        with tab4:
            st.markdown("#### External Integrations")
            
            st.subheader("Azure Services")
            col1, col2 = st.columns(2)
            
            with col1:
                azure_subscription = st.text_input("Azure Subscription ID", type="password")
                azure_resource_group = st.text_input("Resource Group Name")
                storage_account = st.text_input("Storage Account Name")
            
            with col2:
                event_grid_endpoint = st.text_input("Event Grid Endpoint")
                maps_api_key = st.text_input("Azure Maps API Key", type="password")
                sql_connection = st.text_input("SQL Database Connection", type="password")
            
            st.subheader("Third-party Integrations")
            
            integrations = [
                {"name": "Slack", "status": "Connected", "color": "green"},
                {"name": "Microsoft Teams", "status": "Connected", "color": "green"},
                {"name": "PagerDuty", "status": "Disconnected", "color": "red"},
                {"name": "Jira", "status": "Connected", "color": "green"},
                {"name": "Power BI", "status": "Connected", "color": "green"},
            ]
            
            for integration in integrations:
                col1, col2, col3 = st.columns([2, 1, 1])
                with col1:
                    st.write(f"**{integration['name']}**")
                with col2:
                    st.write(f":{integration['color']}[{integration['status']}]")
                with col3:
                    if integration['status'] == "Connected":
                        if st.button("Disconnect", key=f"disconnect_{integration['name']}"):
                            st.warning(f"Disconnected from {integration['name']}")
                    else:
                        if st.button("Connect", key=f"connect_{integration['name']}", type="primary"):
                            st.success(f"Connected to {integration['name']}")
        
        # Save settings
        if st.button("Save All Settings", type="primary"):
            st.success("Settings saved successfully!")
    
    def run(self):
        """Main application entry point"""
        # Check authentication
        if not self.authenticate_user():
            return
        
        # Render sidebar and get navigation
        page, status_filter, time_range = self.render_sidebar()
        
        # Get sample data
        trucks, violations = self.get_sample_data()
        
        # Filter data based on sidebar selections
        if status_filter:
            trucks = [t for t in trucks if t.status in status_filter]
        
        # Auto-refresh logic
        if st.session_state.auto_refresh:
            placeholder = st.empty()
            time.sleep(st.session_state.refresh_interval)
            st.rerun()
        
        # Render selected page
        if "Fleet Overview" in page:
            self.render_fleet_overview(trucks)
        elif "Truck Details" in page:
            self.render_truck_details(trucks)
        elif "Violations" in page:
            self.render_violations_dashboard(violations)
        elif "Route Monitor" in page:
            self.render_route_monitor(trucks)
        elif "Analytics" in page:
            self.render_analytics(trucks, violations)
        elif "Settings" in page:
            self.render_settings()

if __name__ == "__main__":
    # Initialize and run the dashboard
    dashboard = AsphaltTruckDashboard()
    dashboard.run()