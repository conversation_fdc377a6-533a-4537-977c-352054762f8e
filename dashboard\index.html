<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚛 CCTV MLOps Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 { 
            font-size: 2.5rem; 
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 1rem;
            opacity: 0.8;
        }
        .status-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        .status-healthy { color: #4ade80; }
        .status-warning { color: #fbbf24; }
        .footer {
            text-align: center;
            margin-top: 30px;
            opacity: 0.7;
            font-size: 0.9rem;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚛 Computer Vision MLOps Dashboard</h1>
            <div class="subtitle">Real-time CCTV Monitoring for 100 Trucks • 400 Cameras</div>
        </div>

        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value">100</div>
                <div class="metric-label">Total Trucks</div>
            </div>
            <div class="metric-card">
                <div class="metric-value pulse">398</div>
                <div class="metric-label">Active Cameras</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">12</div>
                <div class="metric-label">Violations Today</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">98.5%</div>
                <div class="metric-label">System Health</div>
            </div>
        </div>

        <div class="status-section">
            <h2 style="margin-bottom: 20px; text-align: center;">System Status</h2>
            <div class="status-grid">
                <div class="status-item">
                    <span>PostgreSQL Database</span>
                    <span class="status-healthy">🟢 Healthy</span>
                </div>
                <div class="status-item">
                    <span>Redis Cache</span>
                    <span class="status-healthy">🟢 Healthy</span>
                </div>
                <div class="status-item">
                    <span>MinIO Storage</span>
                    <span class="status-healthy">🟢 Healthy</span>
                </div>
                <div class="status-item">
                    <span>CCTV Processing</span>
                    <span class="status-warning">🟡 Initializing</span>
                </div>
            </div>
        </div>

        <div class="status-section">
            <h2 style="margin-bottom: 20px; text-align: center;">Recent Violations</h2>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="border-bottom: 2px solid rgba(255,255,255,0.2);">
                            <th style="padding: 15px; text-align: left;">Time</th>
                            <th style="padding: 15px; text-align: left;">Truck ID</th>
                            <th style="padding: 15px; text-align: left;">Violation</th>
                            <th style="padding: 15px; text-align: left;">Severity</th>
                            <th style="padding: 15px; text-align: left;">Location</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid rgba(255,255,255,0.1);">
                            <td style="padding: 15px;">14:30</td>
                            <td style="padding: 15px;">TR034</td>
                            <td style="padding: 15px;">Phone Use</td>
                            <td style="padding: 15px; color: #f87171;">High</td>
                            <td style="padding: 15px;">Downtown</td>
                        </tr>
                        <tr style="border-bottom: 1px solid rgba(255,255,255,0.1);">
                            <td style="padding: 15px;">14:15</td>
                            <td style="padding: 15px;">TR071</td>
                            <td style="padding: 15px;">No Seatbelt</td>
                            <td style="padding: 15px; color: #f87171;">High</td>
                            <td style="padding: 15px;">Highway 101</td>
                        </tr>
                        <tr style="border-bottom: 1px solid rgba(255,255,255,0.1);">
                            <td style="padding: 15px;">13:45</td>
                            <td style="padding: 15px;">TR012</td>
                            <td style="padding: 15px;">Speeding</td>
                            <td style="padding: 15px; color: #fbbf24;">Medium</td>
                            <td style="padding: 15px;">Industrial</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="status-section">
            <h2 style="margin-bottom: 20px; text-align: center;">Quick Access</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <a href="http://localhost:9001" target="_blank" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; text-decoration: none; color: white; text-align: center; border: 2px solid transparent; transition: all 0.3s;" onmouseover="this.style.border='2px solid rgba(255,255,255,0.5)'" onmouseout="this.style.border='2px solid transparent'">
                    📁 MinIO Console<br><small>Storage Management</small>
                </a>
                <a href="http://localhost:5432" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; text-decoration: none; color: white; text-align: center; border: 2px solid transparent; transition: all 0.3s;" onmouseover="this.style.border='2px solid rgba(255,255,255,0.5)'" onmouseout="this.style.border='2px solid transparent'">
                    🗄️ PostgreSQL<br><small>Database</small>
                </a>
                <a href="http://localhost:6379" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; text-decoration: none; color: white; text-align: center; border: 2px solid transparent; transition: all 0.3s;" onmouseover="this.style.border='2px solid rgba(255,255,255,0.5)'" onmouseout="this.style.border='2px solid transparent'">
                    ⚡ Redis Cache<br><small>In-Memory Store</small>
                </a>
            </div>
        </div>

        <div class="footer">
            <p>Computer Vision MLOps System | Last updated: <span id="timestamp"></span></p>
            <p style="margin-top: 10px;">🚀 <strong>System Status:</strong> Operational • <strong>Uptime:</strong> <span id="uptime">0m</span></p>
        </div>
    </div>

    <script>
        function updateTimestamp() {
            document.getElementById('timestamp').textContent = new Date().toLocaleString();
        }
        
        function updateUptime() {
            const start = Date.now();
            setInterval(() => {
                const minutes = Math.floor((Date.now() - start) / 60000);
                document.getElementById('uptime').textContent = minutes + 'm';
            }, 60000);
        }
        
        updateTimestamp();
        updateUptime();
        setInterval(updateTimestamp, 30000);
    </script>
</body>
</html>