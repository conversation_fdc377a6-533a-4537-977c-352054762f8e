version: '3.8'

networks:
  cctv-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  redis_data:
  minio_data:
  frigate_config:
  frigate_storage:
  grafana_data:
  prometheus_data:

services:
  # PostgreSQL with PostGIS
  postgres:
    image: postgis/postgis:15-3.4
    container_name: cctv-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: cctv_mlops
      POSTGRES_USER: mlops_user
      POSTGRES_PASSWORD: mlops_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      cctv-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mlops_user -d cctv_mlops"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cctv-redis
    restart: unless-stopped
    command: redis-server --requirepass redis_password --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      cctv-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: cctv-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
      MINIO_DEFAULT_BUCKETS: cctv-videos,cctv-models,cctv-backups
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      cctv-network:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MQTT Broker (Mosquitto)
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: cctv-mosquitto
    restart: unless-stopped
    volumes:
      - ./config/mosquitto/mosquitto.conf:/mosquitto/config/mosquitto.conf
      - ./config/mosquitto/passwd:/mosquitto/config/passwd
    ports:
      - "1883:1883"
      - "9883:9883"
    networks:
      cctv-network:
        ipv4_address: ***********

  # Frigate CCTV Engine
  frigate:
    image: ghcr.io/blakeblackshear/frigate:stable
    container_name: cctv-frigate
    restart: unless-stopped
    shm_size: "1gb"
    environment:
      FRIGATE_RTSP_PASSWORD: frigate_password
    volumes:
      - frigate_config:/config
      - frigate_storage:/media/frigate
      - ./config/frigate/config.yml:/config/config.yml:ro
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "5000:5000"
      - "8554:8554"
      - "8555:8555/tcp"
      - "8555:8555/udp"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - postgres
      - redis
      - mosquitto
    privileged: true
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Ollama LLM Server
  ollama:
    image: ollama/ollama:latest
    container_name: cctv-ollama
    restart: unless-stopped
    volumes:
      - ./data/ollama:/root/.ollama
    ports:
      - "11434:11434"
    networks:
      cctv-network:
        ipv4_address: ***********
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: cctv-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=90d'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      cctv-network:
        ipv4_address: ***********

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: cctv-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3000:3000"
    networks:
      cctv-network:
        ipv4_address: ***********
    depends_on:
      - prometheus

  # Mailhog Email Server
  mailhog:
    image: mailhog/mailhog:latest
    container_name: cctv-mailhog
    restart: unless-stopped
    ports:
      - "8025:8025"
      - "1025:1025"
    networks:
      cctv-network:
        ipv4_address: ***********

  # Nominatim Geocoding Service
  nominatim:
    image: mediagis/nominatim:4.3
    container_name: cctv-nominatim
    restart: unless-stopped
    environment:
      PBF_URL: https://download.geofabrik.de/north-america/us-latest.osm.pbf
      REPLICATION_URL: https://download.geofabrik.de/north-america/us-updates/
      IMPORT_WIKIPEDIA: false
      IMPORT_US_POSTCODES: true
      IMPORT_GB_POSTCODES: false
    volumes:
      - ./data/nominatim:/var/lib/postgresql/14/main
    ports:
      - "8080:8080"
    networks:
      cctv-network:
        ipv4_address: ***********
    shm_size: 2g

  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: cctv-traefik
    restart: unless-stopped
    command:
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    networks:
      cctv-network:
        ipv4_address: ***********
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.localhost`)"
      - "traefik.http.routers.traefik.service=api@internal"