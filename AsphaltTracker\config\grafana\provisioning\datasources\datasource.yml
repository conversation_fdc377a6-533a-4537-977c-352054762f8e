# Grafana datasource provisioning configuration for AsphaltTracker

apiVersion: 1

datasources:
  # Prometheus for metrics
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
      manageAlerts: true
      alertmanagerUid: "alertmanager"

  # InfluxDB for time series data
  - name: InfluxDB
    type: influxdb
    access: proxy
    url: http://influxdb:8086
    database: activity-tracking
    user: admin
    secureJsonData:
      password: password
    jsonData:
      timeInterval: "10s"
      httpMode: "GET"
      keepCookies: []

  # PostgreSQL for application data
  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: asphalt_tracker
    user: postgres
    secureJsonData:
      password: password
    jsonData:
      sslmode: "disable"
      maxOpenConns: 0
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false

  # Alertmanager for alerts
  - name: Alertmanager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    uid: "alertmanager"
    jsonData:
      implementation: "prometheus"

  # Loki for logs (optional)
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: "prometheus"
          matcherRegex: "traceID=(\\w+)"
          name: "TraceID"
          url: "$${__value.raw}"

  # Jaeger for tracing (optional)
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    jsonData:
      tracesToLogs:
        datasourceUid: "loki"
        tags: ["job", "instance", "pod", "namespace"]
        mappedTags: [{"key": "service.name", "value": "service"}]
        mapTagNamesEnabled: false
        spanStartTimeShift: "1h"
        spanEndTimeShift: "1h"
        filterByTraceID: false
        filterBySpanID: false
