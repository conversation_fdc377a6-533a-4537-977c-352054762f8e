{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(git init:*)", "Bash(git branch:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(uv pip install:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(chmod:*)", "Bash(ls:*)", "Bash(cp:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:developer.nvidia.com)", "<PERSON><PERSON>(docker run:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(true)", "Bash(restack:*)", "Bash(npm install:*)", "Bash(pipx install:*)", "WebFetch(domain:restack.io)", "WebFetch(domain:www.restack.io)", "Bash(npx:*)", "WebFetch(domain:docs.restack.io)", "Bash(./restack-get-started-linux-amd64:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(npm run check:*)", "Bash(npm run dev:*)", "Bash(export NODE_ENV=development)", "Bash(node:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm run:*)", "<PERSON><PERSON>(timeout 15s npm run dev)", "Bash(git pull:*)", "Bash(git rebase:*)", "Bash(git push:*)", "WebFetch(domain:www.npmjs.com)", "Bash(git rm:*)", "Bash(git stash:*)"], "deny": []}}